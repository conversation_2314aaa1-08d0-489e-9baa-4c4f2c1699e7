# 生产工单管理页面设计

## 1. 工单状态管理（按业务流程设计文档）

### 1.1 工单执行状态流转
```
待下达 → 已下达 → 生产中 → 已完工 → 已入库 → 已关闭
```

**状态说明**：
- **待下达**：工单已创建并审核通过，等待下达到车间
- **已下达**：工单已下达到车间，自动生成工序任务，可以开始生产
- **生产中**：至少有一个工序已开工
- **已完工**：所有工序都已完工，等待入库
- **已入库**：完工产品已入库
- **已关闭**：工单已关闭，完成整个生产周期

### 1.2 工单列表管理界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🏭 生产工单管理                              [新建工单] [批量下达] [导出]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 执行状态：[全部▼] 审核状态：[全部▼] 部门：[全部▼] 计划完成：[本月▼]       │
│ 工单编号：[_______] 销售订单：[_______]               [搜索] [重置]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单列表                                                                    │
│ ☐ 工单编号    产品名称    计划数量  执行状态    审核状态  领料状态  操作      │
│ ☐ MO240115001 轴承座     100件    🟡待下达    🟢审核通过  未领料    [下达]    │
│ ☐ MO240115002 连接件     200件    🔵生产中    🟢审核通过  已领料    [查看]    │
│ ☐ MO240115003 法兰盘     150件    ✅已完工    🟢审核通过  已领料    [入库]    │
│ ☐ MO240115004 五金件A    300件    🔵生产中    🟢审核通过  已领料    [查看]    │
│ ☐ MO240115005 支架       80件     🟡待下达    🟠审核中    未领料    [查看]    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单统计（按执行状态）                                                      │
│ 总工单：156个  待下达：12个  已下达：8个  生产中：45个  已完工：8个  已入库：83个│
│ 本月新增：28个  本月完工：35个  平均完工周期：5.2天                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. 工单下达管理

### 2.1 工单下达界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 工单下达 - MO240115001                                       [确认下达]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115001  产品名称：轴承座  计划数量：100件                   │
│ 销售订单：SO240110001  部门：机加工车间  计划完成：2024-01-20              │
├─────────────────────────────────────────────────────────────────────────────┤
│ 下达前检查                                                                  │
│ ✅ BOM完整性检查：已通过                                                    │
│ ✅ 物料库存检查：库存充足                                                  │
│ ✅ 工艺路线检查：工艺路线完整                                              │
│ ⚠️  车间产能检查：产能紧张，建议调整计划                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 将自动生成的工序任务                                                        │
│ 工序名称      工作中心    计划工时  标准工时  负责人    备注                │
│ 下料          下料车间    2小时     2小时     张师傅    无                  │
│ 粗加工        机加工车间  8小时     8小时     李师傅    无                  │
│ 精加工        机加工车间  6小时     6小时     王师傅    无                  │
│ 热处理        委外        24小时    24小时    委外      委外工序            │
│ 质检          质检部      1小时     1小时     赵质检    关键质检点          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 下达确认                                                                    │
│ 下达到部门：[机加工车间▼]  下达时间：[2024-01-15 10:00]                   │
│ 备注：[_________________________________________________]                   │
│                                              [取消] [确认下达]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 工单下达业务逻辑
**触发条件**：工单状态为"待下达"（审核通过后）

**下达流程**：
1. **下达前检查**：
   - BOM完整性检查
   - 关键物料库存检查
   - 工艺路线完整性检查
   - 车间产能检查

2. **自动生成工序任务**：
   - 读取工单关联的BOM和工艺路线
   - 根据工序信息自动派工到对应工作中心
   - 生成工序任务单（任务编号：工单号+工序序号）

3. **状态更新**：
   - 工单执行状态：待下达 → 已下达
   - 记录下达时间和下达人

## 3. 工单详情管理

### 3.1 工单详情查看界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 工单详情 - MO240115002                           [编辑] [拆分] [暂停]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单编号：MO240115002  条码：BC240115002  生产计划单：PLAN001              │
│ 产品ID：P001  产品规格ID：SPEC001  计划数量：200件                         │
│ 销售订单：SO240110001  子订单：SUB001  BOM ID：BOM001                      │
│ 部门：机加工车间  创建人：张三  创建时间：2024-01-15 08:00                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 状态信息                                                                    │
│ 执行状态：🔵生产中  审核状态：🟢审核通过  完工状态：进行中  领料状态：已领料│
│ 计划开始：2024-01-15  计划完成：2024-01-20  单据时间：2024-01-15           │
│ 审核人：李四  审核时间：2024-01-15 09:00  审核意见：符合生产要求           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序任务进度                                                                │
│ 工序名称      状态      计划数量  完成数量  开始时间    完成时间    操作      │
│ 下料          已完工    200件    200件    01-15 08:00 01-15 10:00 [查看]    │
│ 粗加工        已完工    200件    200件    01-15 10:30 01-15 18:00 [查看]    │
│ 精加工        生产中    200件    50件     01-16 08:00 -          [报工]    │
│ 热处理        待开工    200件    0件      -          -          [委外]    │
│ 质检          待开工    200件    0件      -          -          [开工]    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 物料领用状态                                                                │
│ 物料名称      规格      计划用量  已领用量  剩余库存  状态      操作        │
│ 钢材A        Q235      100kg    100kg    50kg     已领料    [补料]        │
│ 刀具B        T01       5把      5把      2把      已领料    [补料]        │
│ 切削液        CL-1      10L      10L      15L      已领料    [退料]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 操作记录                                                                    │
│ 时间              操作类型    操作人  说明                                  │
│ 2024-01-15 09:00  审核通过    李四    符合生产要求                          │
│ 2024-01-15 10:00  工单下达    张三    下达到机加工车间                      │
│ 2024-01-15 10:30  工序开工    王五    粗加工工序开始                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. 工单拆分管理

### 4.1 工单拆分界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✂️ 工单拆分 - MO240115001                                       [确认拆分]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 原工单信息                                                                  │
│ 工单编号：MO240115001  产品名称：轴承座  原计划数量：100件                 │
│ 当前状态：🟡待下达  可拆分条件：✅满足                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分方案                                                                    │
│ 拆分原因：[客户要求分批交货▼]                                              │
│ 子工单1：数量[60件] 交期[2024-01-18] 备注[第一批交货]                      │
│ 子工单2：数量[40件] 交期[2024-01-22] 备注[第二批交货]                      │
│                                                          [+添加子工单]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分影响分析                                                                │
│ ✅ 物料分配：可按比例分配                                                  │
│ ✅ 工序任务：将为每个子工单生成独立工序任务                                │
│ ⚠️  交期影响：第二批交期延后2天                                            │
│ ⚠️  成本影响：可能增加5%的管理成本                                         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分确认                                                                    │
│ 拆分后原工单状态将变为"已拆分"，子工单编号：MO240115001-01、MO240115001-02  │
│                                              [取消] [确认拆分]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. 数据库字段映射

### 5.1 erp_machin_header 表字段对应
```
界面元素                    数据库字段                  说明
工单编号                    order_num                  工单唯一标识
条码                        bar_code                   工单条码
产品ID                      material_id                加工的成品id
产品规格ID                  norms_id                   加工的成品规格id
计划数量                    need_num                   需要加工的数量
审核状态                    state                      1.新建 2.审核中 3.审核通过 4.审核拒绝 5.已完成 6.完工入库
执行状态                    execution_state            1.待下达 2.已下达 3.生产中 4.已完工 5.已入库 6.已关闭
完工状态                    completion_state           0.未完工 1.已完工
领料状态                    pick_state                 1.未领料 2.已领料
销售订单                    sales_order_num            关联销售订单号
子订单                      sub_order_num              销售子订单号
BOM ID                      bom_id                     BOM标识
部门                        department_id              生产部门
生产计划单                  production_id              生产计划单号
计划开始时间                start_time                 计划开始时间
计划完成时间                end_time                   计划完成时间
单据时间                    bill_time                  单据创建时间
创建人                      create_id                  创建人ID
创建时间                    create_time                创建时间
审核人                      examine_id                 审核人ID
审核时间                    examine_time               审核时间
审核意见                    examine_content            审核意见
```

### 5.2 工单拆分相关字段
```
父工单ID                    parent_order_id            拆分子工单时的父工单ID
是否拆分父工单              is_split_parent            0-否，1-是
拆分原因                    split_reason               拆分原因说明
拆分时间                    split_time                 拆分操作时间
拆分人                      split_by                   拆分操作人ID
原始数量                    original_quantity          拆分前的原始数量
```

## 6. 状态流转逻辑

### 6.1 工单执行状态流转SQL
```sql
-- 1. 工单下达（待下达 → 已下达）
UPDATE erp_machin_header
SET execution_state = 2,  -- 已下达
    update_time = NOW(),
    update_id = @user_id
WHERE order_num = @order_num
AND execution_state = 1  -- 待下达
AND state = 3;  -- 审核通过

-- 2. 工单开始生产（已下达 → 生产中）
-- 当第一个工序任务开工时自动触发
UPDATE erp_machin_header
SET execution_state = 3  -- 生产中
WHERE id IN (
    SELECT DISTINCT work_order_id
    FROM erp_process_task
    WHERE status = 2  -- 已开工
    AND work_order_id = @work_order_id
)
AND execution_state = 2;  -- 已下达

-- 3. 工单完工（生产中 → 已完工）
-- 当所有工序任务完成时自动触发
UPDATE erp_machin_header
SET execution_state = 4,  -- 已完工
    completion_state = 1  -- 已完工
WHERE id = @work_order_id
AND execution_state = 3  -- 生产中
AND NOT EXISTS (
    SELECT 1 FROM erp_process_task
    WHERE work_order_id = @work_order_id
    AND status NOT IN (4, 6)  -- 非全部完工或已取消
);

-- 4. 工单入库（已完工 → 已入库）
UPDATE erp_machin_header
SET execution_state = 5,  -- 已入库
    state = 6  -- 完工入库（系统状态）
WHERE order_num = @order_num
AND execution_state = 4;  -- 已完工

-- 5. 工单关闭（已入库 → 已关闭）
UPDATE erp_machin_header
SET execution_state = 6  -- 已关闭
WHERE order_num = @order_num
AND execution_state = 5;  -- 已入库
```

### 6.2 工单拆分业务逻辑
```sql
-- 工单拆分主流程
DELIMITER $$
CREATE PROCEDURE SplitWorkOrder(
    IN p_parent_order_id VARCHAR(32),
    IN p_split_reason VARCHAR(200),
    IN p_split_plan JSON,
    IN p_operator_id VARCHAR(32)
)
BEGIN
    DECLARE v_parent_order_num VARCHAR(50);
    DECLARE v_child_count INT DEFAULT 0;
    DECLARE v_child_order_id VARCHAR(32);
    DECLARE v_child_order_num VARCHAR(50);
    DECLARE v_child_quantity DECIMAL(24,6);
    DECLARE v_child_remark VARCHAR(200);
    DECLARE done INT DEFAULT FALSE;

    -- 声明游标处理拆分计划
    DECLARE split_cursor CURSOR FOR
        SELECT quantity, remark FROM JSON_TABLE(
            p_split_plan, '$[*]'
            COLUMNS (
                quantity DECIMAL(24,6) PATH '$.quantity',
                remark VARCHAR(200) PATH '$.remark'
            )
        ) AS jt;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    START TRANSACTION;

    -- 1. 获取父工单信息
    SELECT order_num INTO v_parent_order_num
    FROM erp_machin_header
    WHERE id = p_parent_order_id;

    -- 2. 标记父工单为已拆分
    UPDATE erp_machin_header
    SET is_split_parent = 1,
        split_reason = p_split_reason,
        split_time = NOW(),
        split_by = p_operator_id
    WHERE id = p_parent_order_id;

    -- 3. 创建子工单
    OPEN split_cursor;
    read_loop: LOOP
        FETCH split_cursor INTO v_child_quantity, v_child_remark;
        IF done THEN
            LEAVE read_loop;
        END IF;

        SET v_child_count = v_child_count + 1;
        SET v_child_order_id = CONCAT('WO', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(v_child_count, 3, '0'));
        SET v_child_order_num = CONCAT(v_parent_order_num, '-', LPAD(v_child_count, 2, '0'));

        -- 创建子工单
        INSERT INTO erp_machin_header (
            id, order_num, production_id, department_id, bar_code,
            material_id, norms_id, need_num, start_time, end_time,
            state, execution_state, pick_state, sales_order_num, sub_order_num,
            bom_id, parent_order_id, remark, create_id, create_time
        )
        SELECT
            v_child_order_id, v_child_order_num, production_id, department_id,
            CONCAT('BC', v_child_order_num), material_id, norms_id, v_child_quantity,
            start_time, end_time, 1, 1, 1, sales_order_num, sub_order_num,
            bom_id, p_parent_order_id, v_child_remark, p_operator_id, NOW()
        FROM erp_machin_header
        WHERE id = p_parent_order_id;

    END LOOP;
    CLOSE split_cursor;

    COMMIT;
END$$
DELIMITER ;
```

## 7. 异常状态处理

### 7.1 工单暂停功能
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ⏸️ 工单暂停 - MO240115002                                       [确认暂停]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115002  当前状态：🔵生产中  计划完成：2024-01-20            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 暂停原因                                                                    │
│ 暂停类型：[设备故障▼]                                                      │
│ 暂停原因：[_________________________________________________]               │
│ 预计恢复时间：[2024-01-16 14:00]                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响分析                                                                    │
│ 🔴 交期影响：预计延期1天                                                   │
│ 🔴 后续工单影响：影响3个后续工单                                           │
│ ⚠️  资源影响：释放设备资源，人员可调配                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处理措施                                                                    │
│ ☐ 通知客户交期变更                                                         │
│ ☐ 调整后续工单计划                                                         │
│ ☐ 安排设备维修                                                             │
│                                              [取消] [确认暂停]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.2 工单取消功能
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ❌ 工单取消 - MO240115001                                       [确认取消]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115001  当前状态：🟡待下达  计划数量：100件                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 取消类型：无条件取消（待下达状态）                                          │
│ 取消原因：[客户取消订单▼]                                                  │
│ 详细说明：[_________________________________________________]               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响分析                                                                    │
│ ✅ 无资源损失：未开始生产，无物料消耗                                      │
│ ✅ 无成本损失：仅设计成本，可回收利用                                      │
│ ⚠️  预留物料：需释放预留的物料                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处理措施                                                                    │
│ ☐ 释放预留物料                                                             │
│ ☐ 通知相关部门                                                             │
│ ☐ 更新生产计划                                                             │
│                                              [取消] [确认取消]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 8. 业务规则总结

### 8.1 工单下达规则
1. **前置条件**：工单必须处于"审核通过"状态
2. **检查项目**：BOM完整性、物料库存、工艺路线、车间产能
3. **自动操作**：生成工序任务、更新执行状态、记录操作日志

### 8.2 工单拆分规则
1. **拆分条件**：待下达、已下达（未开工）、生产中（特殊处理）
2. **拆分权限**：生产计划员、生产经理
3. **最小数量**：不能小于工艺要求的最小批量
4. **物料完整性**：每个拆分工单都要有完整的物料配套

### 8.3 状态流转规则
1. **自动流转**：工序开工触发"生产中"，全部完工触发"已完工"
2. **手动流转**：工单下达、入库确认、工单关闭
3. **异常处理**：支持暂停、取消、恢复等异常状态处理
4. **权限控制**：不同状态的操作需要相应权限

## 9. API接口设计

### 9.1 工单列表查询接口

**接口地址：** `GET /api/production/work-orders`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "orderNum": "",                    // 工单编号（模糊查询）
  "materialId": "",                  // 产品ID
  "normsId": "",                     // 规格ID
  "state": null,                     // 审核状态：1-新建，2-审核中，3-审核通过，4-审核拒绝，5-已完成，6-完工入库
  "executionState": null,            // 执行状态：1-待下达，2-已下达，3-生产中，4-已完工，5-已入库，6-已关闭
  "completionState": null,           // 完工状态：0-未完工，1-已完工
  "pickState": null,                 // 领料状态：1-未领料，2-已领料
  "departmentId": "",                // 部门ID
  "startTimeBegin": "",              // 计划开始时间-开始
  "startTimeEnd": "",                // 计划开始时间-结束
  "endTimeBegin": "",                // 计划完成时间-开始
  "endTimeEnd": "",                  // 计划完成时间-结束
  "createTimeBegin": "",             // 创建时间-开始
  "createTimeEnd": "",               // 创建时间-结束
  "salesOrderNum": "",               // 销售订单号
  "isSplitParent": null              // 是否拆分父工单：0-否，1-是
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": "WO240115001",
        "orderNum": "MO240115001",
        "productionId": "PLAN001",
        "departmentId": "DEPT001",
        "barCode": "/uploads/barcode/MO240115001.png",
        "materialId": "MAT001",
        "normsId": "NORM001",
        "needNum": 100.000000,
        "startTime": "2024-01-15 08:00:00",
        "endTime": "2024-01-20 18:00:00",
        "state": 3,
        "executionState": 2,
        "pickState": 1,
        "examineId": "USER001",
        "examineContent": "审核通过",
        "examineTime": "2024-01-15 09:00:00",
        "enclosureInfo": "",
        "remark": "紧急订单",
        "createId": "USER002",
        "createTime": "2024-01-15 08:00:00",
        "salesOrderNum": "SO240115001",
        "subOrderNum": "SUB001",
        "bomId": "BOM001",
        "operTime": "2024-01-15 08:00:00",
        "completionState": 0,
        "processInstanceId": "PROC001",
        "submitType": 2,
        "versionNum": 1,
        "parentOrderId": null,
        "isSplitParent": 0,
        "splitReason": null,
        "splitTime": null,
        "splitBy": null,
        "originalQuantity": null,
        // 关联信息
        "materialName": "轴承座",
        "materialCode": "MAT001",
        "normsName": "Φ50×100",
        "departmentName": "机加工车间",
        "createUserName": "张三",
        "examineUserName": "李四"
      }
    ]
  }
}
```
