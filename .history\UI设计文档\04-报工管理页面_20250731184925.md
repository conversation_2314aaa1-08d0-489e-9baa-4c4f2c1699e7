# 报工管理页面设计

## 1. 报工类型管理（按业务流程设计文档）

### 1.1 三种报工模式
```
1. 快速报工（90%场景）：正常生产，只需填写完成数量和合格数量
2. 异常报工（8%场景）：有质量问题或异常情况，需要详细记录
3. 停机报工（2%场景）：设备故障或其他停机情况，需要记录停机原因
```

**设计理念**：
- **极简操作**：工人只需填写2个核心数据：完成数量、合格数量
- **智能补全**：系统自动计算工时、效率、不合格数量等
- **默认正常**：90%的报工都是正常情况，默认无异常
- **秒级完成**：正常报工15秒内完成，异常报工才需要详细填写

## 2. 报工操作界面

### 2.1 工序报工主界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📱 工序报工                                             [扫码报工] [历史]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 当前任务信息                                                                │
│ 工单号：MO240115001  工序：粗加工  操作工：张师傅                          │
│ 产品：轴承座  规格：Φ50×100  计划数量：100件                              │
│ 已完成：30件  剩余：70件  当前状态：🟢生产中                               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 🚀 快速报工（默认模式）                                                    │
│ 完成数量：[____] 件  合格数量：[____] 件                                  │
│ 开始时间：[2024-01-15 08:00]  结束时间：[2024-01-15 10:30]                │
│                                              [快速提交] [切换异常报工]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 快捷操作                                                                    │
│ [申请补料] [设备维修] [质量异常] [暂停生产] [完工确认]                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 今日报工记录                                                                │
│ 时间              完成数量  合格数量  报工类型  状态                        │
│ 08:00-10:30      20件     20件     快速报工  ✅已确认                      │
│ 10:30-12:00      15件     14件     异常报工  ⚠️待处理                      │
│ 14:00-16:30      25件     25件     快速报工  ✅已确认                      │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 快速报工界面（90%场景）
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ⚡ 快速报工 - 粗加工工序                                    [提交] [取消]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单号：MO240115001  工序：粗加工  操作工：张师傅                          │
│ 任务进度：已完成 30/100 件  本次报工时间：08:00-10:30                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 报工数据（只需填写2项）                                                     │
│ 完成数量：[20] 件  ✅ 数量合理（剩余80件）                                 │
│ 合格数量：[20] 件  ✅ 全部合格                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 系统自动计算                                                                │
│ 不合格数量：0 件（自动计算）                                               │
│ 实际工时：2.5 小时（自动计算）                                             │
│ 工时效率：125%（标准工时2.0小时）                                          │
│ 数量效率：100%（按计划完成）                                               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 状态检查                                                                    │
│ ✅ 数量验证：完成数量合理                                                  │
│ ✅ 质量验证：合格率100%                                                    │
│ ✅ 工时验证：工时效率正常                                                  │
│ ✅ 设备状态：设备运行正常                                                  │
│                                              [保存草稿] [确认提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.3 异常报工界面（8%场景）
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ⚠️ 异常报工 - 粗加工工序                                    [提交] [取消]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单号：MO240115001  工序：粗加工  操作工：张师傅                          │
│ 任务进度：已完成 50/100 件  本次报工时间：10:30-12:00                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 报工数据                                                                    │
│ 完成数量：[15] 件  ⚠️ 低于预期（计划20件）                                │
│ 合格数量：[14] 件  ⚠️ 合格率93.3%                                         │
│ 不合格数量：[1] 件（自动计算）                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 异常详情                                                                    │
│ 异常类型：[质量异常▼]                                                      │
│ 异常原因：[材料硬度超标，刀具磨损严重]                                      │
│ 不合格明细：                                                                │
│ 缺陷类型      数量  原因说明                                               │
│ 尺寸超差      1件   材料硬度高，切削困难                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处理措施                                                                    │
│ 已采取措施：[更换新刀具，调整切削参数]                                      │
│ 预防措施：[联系供应商确认材料规格]                                          │
│ 影响评估：[延期0.5小时，增加刀具成本50元]                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 自动通知                                                                    │
│ 🔔 质检员：李质检（质量异常需要确认）                                      │
│ 🔔 班组长：王班长（效率异常需要关注）                                      │
│ 🔔 设备员：赵师傅（刀具更换需要记录）                                      │
│                                              [保存草稿] [确认提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.4 停机报工界面（2%场景）
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔴 停机报工 - 粗加工工序                                    [提交] [取消]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单号：MO240115001  工序：粗加工  操作工：张师傅                          │
│ 任务进度：已完成 70/100 件  停机时间：14:00-16:30                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 停机前报工                                                                  │
│ 停机前完成：[20] 件  停机前合格：[20] 件                                   │
│ 停机时间：[2.5] 小时  停机损失：约50件产能                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 停机详情                                                                    │
│ 停机类型：[设备故障▼]                                                      │
│ 故障设备：[数控车床CNC-001▼]                                               │
│ 故障现象：[主轴异响，无法正常切削]                                          │
│ 故障等级：[严重▼]  影响范围：[本工序停产▼]                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处理过程                                                                    │
│ 报修时间：[2024-01-15 14:00]  维修人员：[设备部-赵师傅▼]                  │
│ 故障原因：[主轴轴承磨损]                                                    │
│ 处理措施：[更换主轴轴承，重新校准]                                          │
│ 修复时间：[2024-01-15 16:30]  维修费用：[1200] 元                         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响分析                                                                    │
│ 生产影响：延期2.5小时，影响产能50件                                        │
│ 成本影响：维修费用1200元，人工损失300元                                    │
│ 后续安排：[安排加班补产能，通知下游工序调整计划]                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 自动通知                                                                    │
│ 🔔 生产主管：王主管（生产计划调整）                                        │
│ 🔔 设备主管：李主管（设备维护记录）                                        │
│ 🔔 计划员：张计划（后续计划调整）                                          │
│                                              [保存草稿] [确认提交]         │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 3. 报工记录管理

### 3.1 报工记录列表
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 报工记录管理                                         [导出] [统计分析]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 工单号：[_______] 工序：[全部▼] 操作工：[全部▼] 日期：[今日▼]            │
│ 报工类型：[全部▼] 状态：[全部▼]                       [搜索] [重置]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 报工记录列表                                                                │
│ 报工单号        工单号      工序    操作工  报工时间      类型    状态      │
│ WR240115001    MO240115001 粗加工  张师傅  01-15 08:00  快速    ✅已确认  │
│ WR240115002    MO240115001 粗加工  张师傅  01-15 10:30  异常    ⚠️待处理  │
│ WR240115003    MO240115002 精加工  李师傅  01-15 09:00  快速    ✅已确认  │
│ WR240115004    MO240115003 装配    王师傅  01-15 14:00  停机    🔴待审核  │
│ WR240115005    MO240115004 检验    赵师傅  01-15 16:00  快速    ✅已确认  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 统计汇总                                                                    │
│ 今日报工：68次  快速报工：61次(89.7%)  异常报工：5次(7.4%)  停机报工：2次(2.9%) │
│ 平均效率：108.5%  合格率：96.8%  异常处理中：3次  待审核：1次              │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 报工记录详情
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📄 报工记录详情 - WR240115002                           [编辑] [审核]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 报工单号：WR240115002  报工类型：异常报工  状态：⚠️待处理                 │
│ 工单号：MO240115001  工序：粗加工  操作工：张师傅                          │
│ 报工时间：2024-01-15 10:30  工作时长：1.5小时                              │
├─────────────────────────────────────────────────────────────────────────────┤
│ 生产数据                                                                    │
│ 完成数量：15件  合格数量：14件  不合格数量：1件                            │
│ 计划数量：20件  完成率：75%  合格率：93.3%                                 │
│ 标准工时：1.0小时  实际工时：1.5小时  工时效率：66.7%                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 异常详情                                                                    │
│ 异常类型：质量异常  异常等级：一般                                          │
│ 异常原因：材料硬度超标，刀具磨损严重                                        │
│ 不合格明细：尺寸超差 1件 - 材料硬度高，切削困难                           │
│ 处理措施：更换新刀具，调整切削参数                                          │
│ 预防措施：联系供应商确认材料规格                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处理记录                                                                    │
│ 时间              处理人    处理内容                                        │
│ 2024-01-15 10:30  张师傅    提交异常报工                                   │
│ 2024-01-15 11:00  李质检    确认质量异常，同意处理措施                     │
│ 2024-01-15 11:30  王班长    审核通过，安排后续生产                         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 关联信息                                                                    │
│ 关联工序任务：PT240115001  任务状态：生产中                               │
│ 关联设备：CNC-001  设备状态：正常运行                                      │
│ 后续报工：WR240115006（已正常生产）                                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. 报工审核管理

### 4.1 异常报工审核
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✅ 异常报工审核                                         [批量审核] [导出]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 待审核列表                                                                  │
│ ☐ 报工单号      工单号      工序    操作工  异常类型  提交时间    紧急度    │
│ ☑ WR240115002  MO240115001 粗加工  张师傅  质量异常  10:30      🟡一般    │
│ ☐ WR240115004  MO240115003 装配    王师傅  设备故障  14:00      🔴紧急    │
│ ☐ WR240115007  MO240115005 检验    赵师傅  物料异常  16:00      🟢普通    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 审核详情 - WR240115002                                                      │
│ 异常类型：质量异常  异常原因：材料硬度超标，刀具磨损严重                    │
│ 影响评估：完成率75%，合格率93.3%，延期0.5小时                              │
│ 处理措施：更换新刀具，调整切削参数                                          │
│ 预防措施：联系供应商确认材料规格                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 审核意见                                                                    │
│ 审核人：[王班长▼]  审核时间：[2024-01-15 11:30]                           │
│ 审核结果：[通过▼]  处理建议：[同意处理措施，加强供应商管理]                │
│ 审核备注：[_________________________________________________]               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 后续安排                                                                    │
│ 🔄 自动操作：审核通过后自动更新工序状态                                   │
│ 📋 质量记录：自动记录质量异常信息                                          │
│ 📞 供应商通知：自动发送材料质量反馈                                        │
│                                              [驳回] [审核通过] [转上级]     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 停机报工审核
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔴 停机报工审核 - WR240115004                           [审核通过]         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 停机信息                                                                    │
│ 报工单号：WR240115004  工单号：MO240115003  工序：装配                     │
│ 操作工：王师傅  停机时间：2024-01-15 14:00-16:30（2.5小时）                │
│ 停机类型：设备故障  故障设备：装配线AL-002                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 故障详情                                                                    │
│ 故障现象：传送带异响，无法正常运转                                          │
│ 故障等级：严重  影响范围：装配工序停产                                      │
│ 故障原因：传送带轴承损坏                                                    │
│ 处理措施：更换传送带轴承，重新调试                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响分析                                                                    │
│ 生产影响：停产2.5小时，影响产能30件                                        │
│ 成本影响：维修费用800元，人工损失200元                                     │
│ 计划调整：需要安排加班2小时补产能                                          │
│ 交期影响：可能延期0.5天，需要通知客户                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 审核评估                                                                    │
│ 故障责任：[设备老化▼]  预防措施：[加强设备保养]                           │
│ 处理及时性：✅及时报修  处理效果：✅故障已修复                             │
│ 成本合理性：✅维修费用合理  影响可控性：✅影响在可控范围内                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 审核决策                                                                    │
│ 审核人：[李主管▼]  审核时间：[2024-01-15 17:00]                           │
│ 审核结果：[通过▼]  责任认定：[设备自然老化，无人为责任]                    │
│ 改进建议：[制定设备预防性维护计划]                                          │
│                                              [驳回] [审核通过] [上报]       │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. 报工统计分析

### 5.1 报工效率分析
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📊 报工效率分析                                         [导出] [打印]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 时间范围：[本月▼] 2024年1月  分析维度：[操作工▼]                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 操作工效率排行                                                              │
│ 操作工    报工次数  平均效率  合格率  异常次数  快速报工率  综合评分        │
│ 张师傅    45次     125%      98.5%   2次      95.6%      A级             │
│ 李师傅    38次     118%      97.2%   3次      92.1%      A级             │
│ 王师傅    42次     108%      96.8%   4次      90.5%      B级             │
│ 赵师傅    35次     102%      95.5%   5次      85.7%      B级             │
│ 陈师傅    28次     95%       94.2%   6次      82.1%      C级             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序效率分析                                                                │
│ 工序名称    标准工时  平均实际工时  工时效率  完成率  异常率  瓶颈指数      │
│ 下料        2.0小时   1.8小时      111%      98%     2%      低           │
│ 粗加工      3.0小时   2.7小时      111%      95%     5%      中           │
│ 精加工      4.0小时   4.2小时      95%       92%     8%      高           │
│ 装配        2.5小时   2.8小时      89%       90%     10%     高           │
│ 检验        1.0小时   1.1小时      91%       96%     4%      中           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 异常分析                                                                    │
│ 异常类型      发生次数  占比   主要原因                        改进建议    │
│ 质量异常      15次     60%    材料质量问题，工艺参数不当        加强质检    │
│ 设备故障      6次      24%    设备老化，维护不及时              预防维护    │
│ 物料异常      3次      12%    物料短缺，规格不符                优化采购    │
│ 其他异常      1次      4%     人员技能不足                      技能培训    │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 6. 数据库字段映射

### 6.1 erp_work_report 表字段对应
```
界面元素                    数据库字段                  说明
报工单号                    report_no                  报工单唯一编号
工序任务ID                  task_id                    关联工序任务
生产工单ID                  work_order_id              关联生产工单
工序ID                      process_id                 关联工序
操作工ID                    operator_id                操作工标识
操作工姓名                  operator_name              操作工姓名
报工类型                    report_type                1-快速报工，2-异常报工，3-停机报工
完成数量                    completed_qty              本次完成数量
合格数量                    qualified_qty              本次合格数量
不合格数量                  unqualified_qty            本次不合格数量
开始时间                    start_time                 工序开始时间
结束时间                    end_time                   工序结束时间
实际工时                    actual_hours               实际工作小时数
标准工时                    standard_hours             标准工作小时数
工时效率                    time_efficiency            工时效率百分比
数量效率                    quantity_efficiency        数量效率百分比
综合效率                    overall_efficiency         综合效率百分比
异常类型                    exception_type             异常类型代码
异常原因                    exception_reason           异常原因描述
处理措施                    handling_measures          已采取的处理措施
预防措施                    prevention_measures        预防措施建议
设备ID                      equipment_id               使用设备标识
设备状态                    equipment_status           设备运行状态
报工状态                    status                     1-草稿，2-已提交，3-已审核，4-已关闭
审核人                      reviewer_id                审核人员ID
审核时间                    review_time                审核完成时间
审核意见                    review_comment             审核意见内容
创建时间                    create_time                记录创建时间
更新时间                    update_time                记录更新时间
备注                        remark                     其他备注信息
```

## 7. 状态流转SQL示例

### 7.1 快速报工流程
```sql
-- 创建快速报工记录
INSERT INTO erp_work_report (
    id, report_no, task_id, work_order_id, process_id,
    operator_id, operator_name, report_type, completed_qty, qualified_qty,
    unqualified_qty, start_time, end_time, actual_hours, standard_hours,
    status, create_time
) VALUES (
    'WR001', 'WR240115001', 'PT001', 'WO001', 'P001',
    'OP001', '张师傅', 1, 20.000, 20.000,
    0.000, '2024-01-15 08:00:00', '2024-01-15 10:30:00', 2.5, 2.0,
    2, NOW()
);

-- 自动计算效率
UPDATE erp_work_report
SET time_efficiency = (standard_hours / actual_hours) * 100,
    quantity_efficiency = (completed_qty / (SELECT plan_qty FROM erp_process_task WHERE id = task_id)) * 100,
    overall_efficiency = ((standard_hours / actual_hours) + (qualified_qty / completed_qty)) / 2 * 100
WHERE id = 'WR001';

-- 更新工序任务进度
UPDATE erp_process_task
SET completed_qty = completed_qty + 20.000,
    qualified_qty = qualified_qty + 20.000,
    actual_hours = actual_hours + 2.5,
    last_report_time = NOW()
WHERE id = 'PT001';
```

### 7.2 异常报工流程
```sql
-- 创建异常报工记录
INSERT INTO erp_work_report (
    id, report_no, task_id, work_order_id, process_id,
    operator_id, operator_name, report_type, completed_qty, qualified_qty,
    unqualified_qty, start_time, end_time, actual_hours, standard_hours,
    exception_type, exception_reason, handling_measures, prevention_measures,
    status, create_time
) VALUES (
    'WR002', 'WR240115002', 'PT001', 'WO001', 'P001',
    'OP001', '张师傅', 2, 15.000, 14.000,
    1.000, '2024-01-15 10:30:00', '2024-01-15 12:00:00', 1.5, 1.0,
    '质量异常', '材料硬度超标，刀具磨损严重', '更换新刀具，调整切削参数', '联系供应商确认材料规格',
    2, NOW()
);

-- 异常报工需要审核
UPDATE erp_work_report
SET status = 2, remark = '等待班组长审核'
WHERE id = 'WR002';

-- 审核通过
UPDATE erp_work_report
SET status = 3, reviewer_id = 'MGR001', review_time = NOW(),
    review_comment = '异常处理措施合理，审核通过'
WHERE id = 'WR002';
```

### 7.3 停机报工流程
```sql
-- 创建停机报工记录
INSERT INTO erp_work_report (
    id, report_no, task_id, work_order_id, process_id,
    operator_id, operator_name, report_type, completed_qty, qualified_qty,
    unqualified_qty, start_time, end_time, actual_hours, standard_hours,
    exception_type, exception_reason, handling_measures, equipment_id, equipment_status,
    status, create_time
) VALUES (
    'WR003', 'WR240115004', 'PT003', 'WO003', 'P003',
    'OP003', '王师傅', 3, 20.000, 20.000,
    0.000, '2024-01-15 14:00:00', '2024-01-15 16:30:00', 2.5, 2.0,
    '设备故障', '传送带异响，无法正常运转', '更换传送带轴承，重新调试', 'EQ002', '维修中',
    2, NOW()
);

-- 停机报工需要主管审核
UPDATE erp_work_report
SET status = 2, remark = '设备故障停机，等待主管审核'
WHERE id = 'WR003';

-- 主管审核通过
UPDATE erp_work_report
SET status = 3, reviewer_id = 'MGR002', review_time = NOW(),
    review_comment = '设备故障处理及时，审核通过'
WHERE id = 'WR003';
```

## 8. 业务规则说明

### 8.1 报工数据验证规则
1. **数量验证**：完成数量不能超过剩余计划数量
2. **质量验证**：合格数量不能大于完成数量
3. **工时验证**：实际工时不能超过标准工时的200%
4. **状态验证**：只有"生产中"状态的工序才能报工

### 8.2 自动计算规则
1. **不合格数量**：完成数量 - 合格数量
2. **工时效率**：标准工时 / 实际工时 × 100%
3. **数量效率**：完成数量 / 计划数量 × 100%
4. **综合效率**：(工时效率 + 质量效率) / 2

### 9.3 异常处理规则
1. **质量异常**：合格率低于95%自动标记为异常
2. **效率异常**：工时效率低于80%自动标记为异常
3. **设备异常**：设备故障自动通知设备部门
4. **物料异常**：物料不足自动生成补料申请

### 9.4 审核规则
1. **快速报工**：系统自动审核通过
2. **异常报工**：需要班组长审核
3. **停机报工**：需要生产主管审核
4. **重大异常**：需要上级主管审核

---

**设计说明**：
- 严格按照业务流程设计文档中的报工管理流程设计
- 完全对应 erp_work_report 表结构
- 支持三种报工模式：快速报工（90%）、异常报工（8%）、停机报工（2%）
- 实现极简操作理念，正常报工15秒内完成
- 支持移动端操作和扫码报工
- 提供完整的审核流程和统计分析功能
- 集成自动计算和智能验证机制
