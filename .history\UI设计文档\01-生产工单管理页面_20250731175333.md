# 生产工单管理页面设计

## 1. 工单列表管理

### 1.1 工单列表界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🏭 生产工单管理                                      [新建工单] [导入]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 工单状态：[全部▼] 优先级：[全部▼] 车间：[全部▼] 计划完成：[本月▼]         │
│ 工单编号：[_______] 产品名称：[_______]               [搜索] [重置]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单列表                                                                    │
│ ☐ 工单编号    产品名称    计划数量  完成进度  状态      领料状态  操作      │
│ ☐ MO240115001 轴承座     100件    0%       🟡新建     未领料    [下达]      │
│ ☐ MO240115002 连接件     200件    25%      🟢已下达   已领料    [查看]      │
│ ☐ MO240115003 法兰盘     150件    100%     ✅已完工   已领料    [入库]      │
│ ☐ MO240115004 五金件A    300件    93%      🟢已下达   已领料    [查看]      │
│ ☐ MO240115005 支架       80件     0%       🟠审核中   未领料    [查看]      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单统计                                                                    │
│ 总工单：156个  待下达：12个  生产中：45个  已完工：8个  已关闭：91个        │
│ 本月新增：28个  本月完工：35个  平均完工周期：5.2天                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 工单详情查看
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 工单详情 - MO240115002                           [编辑] [拆分] [取消]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单编号：MO240115002  产品编码：P001  产品名称：连接件                    │
│ 产品规格：50*30*10mm  计划数量：200件  单位：件                            │
│ 客户名称：华强机械  销售订单：SO240110001  优先级：🟠高                    │
│ 车间：机加工车间  BOM版本：V1.2  工艺路线：R001                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 进度信息                                                                    │
│ 工单状态：🟢生产中  计划开始：2024-01-15  计划完成：2024-01-20              │
│ 实际开始：2024-01-15  预计完成：2024-01-21  完成进度：25%                  │
│ 计划数量：200件  完成数量：50件  合格数量：48件  不合格数量：2件            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序进度                                                                    │
│ ┌──────────┬──────────┬──────┬──────┬──────┬──────────┐                     │
│ │ 工序名称 │ 计划数量 │ 完成 │ 合格 │ 状态 │ 负责人   │                     │
│ ├──────────┼──────────┼──────┼──────┼──────┼──────────┤                     │
│ │ 下料     │ 200件    │ 200  │ 200  │ ✅完工│ 张师傅   │                     │
│ │ 粗加工   │ 200件    │ 150  │ 148  │ 🟢进行│ 李师傅   │                     │
│ │ 精加工   │ 200件    │ 50   │ 48   │ 🟢进行│ 王师傅   │                     │
│ │ 热处理   │ 200件    │ 0    │ 0    │ ⏳待工│ 委外     │                     │
│ │ 质检     │ 200件    │ 0    │ 0    │ ⏳待工│ 赵质检   │                     │
│ └──────────┴──────────┴──────┴──────┴──────┴──────────┘                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 物料状态                                                                    │
│ 已领物料：钢材100kg、切削液5L、刀具2套  剩余物料：钢材20kg、切削液2L       │
│ 委外物料：热处理剂5kg（待发料）  异常物料：无                              │
├─────────────────────────────────────────────────────────────────────────────┤
│ 操作记录                                                                    │
│ 2024-01-15 09:00  工单下达  张计划员  备注：按计划正常下达                 │
│ 2024-01-15 10:30  开始生产  李师傅    备注：下料工序开工                   │
│ 2024-01-16 14:20  异常记录  王师傅    备注：刀具磨损，已更换               │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. 工单下达管理

### 2.1 工单下达界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🚀 工单下达 - MO240115001                           [确认下达] [取消]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115001  产品：轴承座  数量：100件  优先级：🔴紧急            │
│ 计划开始：2024-01-16  计划完成：2024-01-22  车间：机加工车间               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 下达前检查                                                                  │
│ ✅ BOM完整性：已确认  ✅ 工艺路线：已确认  ✅ 车间产能：充足                │
│ ⚠️ 关键物料：45#钢棒库存不足20kg  🔴 设备状态：机床002维修中               │
│ 📋 质检要求：精加工、热处理工序需要质检                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 自动生成内容                                                                │
│ 🔧 工序任务：将生成5个工序任务（下料、粗加工、精加工、热处理、质检）       │
│ 📦 领料单：将生成领料单LR240116001（排除委外工序物料）                     │
│ 📅 排程计划：将自动安排各工序的计划时间                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 风险提示                                                                    │
│ ⚠️ 物料风险：45#钢棒库存不足，建议先采购或调整计划                        │
│ ⚠️ 设备风险：机床002维修中，可能影响精加工工序                             │
│ ✅ 交期风险：按当前产能可按时完成                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 下达确认                                                                    │
│ 下达方式：◉ 立即下达  ○ 定时下达[2024-01-16 08:00]                        │
│ 特殊说明：[紧急订单，优先安排生产]                                         │
│ 下达人：张计划员  下达时间：2024-01-15 16:30                              │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 3. 工单状态管理

### 3.1 状态流转界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔄 工单状态管理 - MO240115002                       [状态变更] [历史]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 当前状态流转图                                                              │
│ 待下达 → 已下达 → 🟢生产中 → 已完工 → 已入库 → 已关闭                     │
│         ✅      ✅       ⏳      ⏳      ⏳                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 状态详情                                                                    │
│ 当前状态：🟢生产中  状态时间：2024-01-15 10:30                             │
│ 触发条件：下料工序开工  触发人：李师傅                                     │
│ 下一状态：已完工  触发条件：所有工序完工                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 可执行操作                                                                  │
│ [暂停工单] - 设备故障、物料短缺等异常情况                                  │
│ [取消工单] - 客户取消、设计变更（需要特殊权限）                            │
│ [强制完工] - 紧急情况下强制完工（需要经理权限）                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 状态变更历史                                                                │
│ 2024-01-15 09:00  待下达 → 已下达    张计划员  工单正常下达                │
│ 2024-01-15 10:30  已下达 → 生产中    李师傅    下料工序开工                │
│ 2024-01-16 14:20  生产中 → 生产中    系统      粗加工工序开工              │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 工单暂停界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ⏸️ 工单暂停 - MO240115002                           [确认暂停] [取消]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 暂停原因                                                                    │
│ ◉ 设备故障  ○ 物料短缺  ○ 质量问题  ○ 人员不足  ○ 其他                   │
│ 具体说明：[机床002主轴故障，预计维修2天]                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响评估                                                                    │
│ 影响工序：精加工（当前进行中）  影响数量：150件                            │
│ 预计延期：2天  对交期影响：可能延期至2024-01-23                            │
│ 后续工序：热处理、质检将相应延期                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处理方案                                                                    │
│ ◉ 等待设备修复  ○ 转移其他设备  ○ 委外加工  ○ 调整计划                   │
│ 预计恢复时间：[2024-01-18 08:00]                                           │
│ 应急措施：[联系设备厂家加急维修，准备备用设备]                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 通知设置                                                                    │
│ ☑ 通知客户  ☑ 通知生产经理  ☑ 通知相关操作工                              │
│ 暂停人：王主任  暂停时间：2024-01-16 15:00                                 │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. 工单拆分管理

### 4.1 工单拆分界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✂️ 工单拆分 - MO240115004                           [确认拆分] [取消]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 原工单信息                                                                  │
│ 工单编号：MO240115004  产品：五金件A  原数量：300件  当前状态：🟢生产中    │
│ 计划完成：2024-01-25  客户：华强机械  优先级：🟢普通                       │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分原因                                                                    │
│ ◉ 客户要求分批交货  ○ 产能限制  ○ 物料短缺  ○ 设备安排  ○ 质量风险       │
│ 具体说明：[客户要求分三批交货，第一批100件1月20日，其余分两批]              │
├─────────────────────────────────────────────────────────────────────────────┤
│ 当前生产状态                                                                │
│ 下料：✅完成300件  粗加工：🟢进行中280件  精加工：⏳待工0件                │
│ 在制品分布：下料完成300件，粗加工完成200件、进行中80件                     │
│ 拆分基数：200件（粗加工完工数量）                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分方案设计                                                                │
│ 拆分方式：◉ 按数量拆分  ○ 按比例拆分  ○ 平均拆分                          │
│ 子工单数量：[3]个                                                          │
│ ┌──────────┬──────────┬──────────┬──────────┬──────────┐                     │
│ │ 子工单   │ 拆分数量 │ 交期     │ 优先级   │ 备注     │                     │
│ ├──────────┼──────────┼──────────┼──────────┼──────────┤                     │
│ │ MO..004-01│ 100件   │ 01-20    │ 🔴紧急   │ 第一批   │                     │
│ │ MO..004-02│ 100件   │ 01-23    │ 🟠高     │ 第二批   │                     │
│ │ MO..004-03│ 100件   │ 01-25    │ 🟢普通   │ 第三批   │                     │
│ └──────────┴──────────┴──────────┴──────────┴──────────┘                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 在制品分配方案                                                              │
│ 方案选择：◉ 按完工工序基数拆分（推荐）  ○ 精确在制品拆分                  │
│ 分配预览：                                                                  │
│ - 子工单001：粗加工完成67件，后续工序待重新安排                            │
│ - 子工单002：粗加工完成67件，后续工序待重新安排                            │
│ - 子工单003：粗加工完成66件，后续工序待重新安排                            │
│ - 剩余处理：粗加工进行中80件完成后按比例补充到各子工单                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响分析                                                                    │
│ 📦 物料影响：需要重新分配剩余物料到各子工单                                │
│ 🏭 产能影响：各子工单可并行生产，提高整体效率                              │
│ 📅 交期影响：第一批可提前5天交货，满足客户要求                             │
│ 💰 成本影响：已发生成本按比例分摊，管理成本略有增加                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 风险提示                                                                    │
│ ⚠️ 拆分后各子工单需要重新安排生产计划                                      │
│ ⚠️ 在制品80件完成后需要手工分配到各子工单                                  │
│ ⚠️ 质量追溯将按子工单分别进行                                              │
├─────────────────────────────────────────────────────────────────────────────┤
│ 审批信息                                                                    │
│ 申请人：张计划员  申请时间：2024-01-16 14:00                               │
│ 申请理由：[客户紧急要求分批交货，第一批用于试装]                           │
│ 审批状态：⏳待审批  审批人：[选择审批人▼]                                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 拆分后管理界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📊 拆分工单管理 - MO240115004                       [查看子工单] [合并]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分关系图                                                                  │
│ 父工单：MO240115004（五金件A，300件）                                      │
│ ├─ 子工单001：MO240115004-01（100件，🟢生产中，进度60%）                   │
│ ├─ 子工单002：MO240115004-02（100件，🟡已下达，进度0%）                    │
│ └─ 子工单003：MO240115004-03（100件，⏳待下达，进度0%）                    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 整体进度统计                                                                │
│ 总体进度：20%（60/300件）  按期完成率：100%                                │
│ 第一批：60/100件（60%）预计01-19完成  ✅按期                               │
│ 第二批：0/100件（0%）预计01-23完成   ✅按期                                │
│ 第三批：0/100件（0%）预计01-25完成   ✅按期                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ 子工单详情                                                                  │
│ ┌──────────┬──────┬──────┬──────┬──────┬──────┬──────────┐                   │
│ │ 子工单   │ 数量 │ 完成 │ 状态 │ 交期 │ 进度 │ 操作     │                   │
│ ├──────────┼──────┼──────┼──────┼──────┼──────┼──────────┤                   │
│ │ 004-01   │ 100  │ 60   │ 生产中│01-20 │ 60%  │ [查看]   │                   │
│ │ 004-02   │ 100  │ 0    │ 已下达│01-23 │ 0%   │ [开工]   │                   │
│ │ 004-03   │ 100  │ 0    │ 待下达│01-25 │ 0%   │ [下达]   │                   │
│ └──────────┴──────┴──────┴──────┴──────┴──────┴──────────┘                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 成本分摊情况                                                                │
│ 已发生成本：12,000元  分摊方式：按数量比例                                 │
│ - 子工单001：4,000元（33.3%）  - 子工单002：4,000元（33.3%）              │
│ - 子工单003：4,000元（33.3%）                                              │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分履历                                                                    │
│ 拆分时间：2024-01-16 15:30  拆分人：张计划员  审批人：李经理               │
│ 拆分原因：客户要求分批交货  拆分方式：按数量拆分                           │
│ 效果评估：第一批提前交货，客户满意度提升                                   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. 数据库字段映射

### 5.1 erp_machin_header 表字段对应（现有系统）
```
界面元素                    数据库字段                  说明
工单编号                    order_num                  工单唯一标识
产品ID                      material_id                加工的成品id
产品规格ID                  norms_id                   加工的成品规格id
计划数量                    need_num                   需要加工的数量
工单状态                    state                      1.新建 2.审核中 3.审核通过 4.审核拒绝 5.已完成 6.完工入库
完工状态                    completion_state           0.未完工 1.已完工
领料状态                    pick_state                 1.未领料 2.已领料
部门ID                      department_id              部门id
条形码路径                  bar_code                   条形码路径
计划开始时间                start_time                 计划开始加工时间
计划完成时间                end_time                   计划完成加工时间
审核人                      examine_id                 审核人
审核意见                    examine_content            审核意见
审核时间                    examine_time               审核时间
销售订单号                  sales_order_num            关联销售订单
子订单号                    sub_order_num              子订单号
BOM ID                      bom_id                     BOM ID
单据时间                    oper_time                  单据时间
生产计划单ID                production_id              生产计划单id
附件信息                    enclosure_info             附件id
备注                        remark                     备注
创建人                      create_id                  创建人
创建时间                    create_time                创建时间
工作流程ID                  process_instance_id        工作流程id
提交类型                    submit_type                1.走工作流提交 2.直接提交
版本号                      version_num                版本号，处理数据幂等性问题
```

### 5.2 工单状态流转逻辑（基于现有系统）
```sql
-- 工单审核通过（下达）
UPDATE erp_machin_header
SET state = 3,  -- 审核通过
    examine_id = 'USER001',
    examine_content = '审核通过，可以开始生产',
    examine_time = NOW()
WHERE order_num = 'MO240115001'
AND state = 2;  -- 审核中

-- 工单开始生产（通过工序任务开工触发）
-- 当第一个工序任务开工时，工单状态保持为3（审核通过），通过工序进度体现生产状态

-- 工单完工状态更新
UPDATE erp_machin_header
SET completion_state = 1,  -- 已完工
    state = 5  -- 已完成
WHERE order_num = 'MO240115001'
AND state = 3;  -- 审核通过状态

-- 工单完工入库
UPDATE erp_machin_header
SET state = 6  -- 完工入库
WHERE order_num = 'MO240115001'
AND state = 5  -- 已完成
AND completion_state = 1;  -- 已完工
```

### 5.3 状态映射关系
```
现有系统状态(state)         UI显示状态           业务含义
1                          🟡待下达            新建状态，等待下达
2                          🟠审核中            提交审核，等待审批
3                          🟢已下达            审核通过，可以开始生产
4                          🔴审核拒绝          审核拒绝，需要修改
5                          ✅已完工            生产完成
6                          📦已入库            完工入库，工单关闭

完工状态(completion_state)   UI显示             业务含义
0                          进行中              未完工
1                          已完工              已完工

领料状态(pick_state)        UI显示             业务含义
1                          未领料              未领料
2                          已领料              已领料
```

### 5.4 工单拆分扩展字段（需要添加到现有表）
```sql
-- 为支持工单拆分功能，需要在erp_machin_header表中添加以下字段：
ALTER TABLE erp_machin_header
ADD COLUMN parent_order_id varchar(32) DEFAULT NULL COMMENT '父工单ID（拆分子工单时使用）',
ADD COLUMN is_split_parent tinyint(1) DEFAULT 0 COMMENT '是否为拆分父工单：0-否，1-是',
ADD COLUMN split_reason varchar(200) DEFAULT NULL COMMENT '拆分原因',
ADD COLUMN split_time datetime DEFAULT NULL COMMENT '拆分时间',
ADD COLUMN split_by varchar(32) DEFAULT NULL COMMENT '拆分人ID',
ADD COLUMN original_quantity decimal(24,6) DEFAULT NULL COMMENT '原始数量（拆分前数量）';

-- 工单拆分操作示例
-- 1. 标记父工单为已拆分
UPDATE erp_machin_header
SET is_split_parent = 1,
    split_reason = '客户要求分批交货',
    split_time = NOW(),
    split_by = 'USER001',
    original_quantity = need_num
WHERE order_num = 'MO240115004';

-- 2. 创建子工单
INSERT INTO erp_machin_header
(id, order_num, production_id, department_id, bar_code, material_id, norms_id,
 need_num, start_time, end_time, state, pick_state, remark, create_id, create_time,
 sales_order_num, sub_order_num, bom_id, oper_time, parent_order_id)
SELECT
  UUID(), 'MO240115004-01', production_id, department_id, bar_code, material_id, norms_id,
  100.000000, start_time, '2024-01-20 23:59:59', 1, 1, '拆分子工单-第一批', create_id, NOW(),
  sales_order_num, sub_order_num, bom_id, oper_time, id
FROM erp_machin_header
WHERE order_num = 'MO240115004';
```

## 6. 核心功能实现要点

### 6.1 工单下达前检查逻辑（基于现有系统）
```javascript
// 工单下达前检查函数（基于erp_machin_header表）
function checkOrderRelease(orderId) {
  const order = getOrderById(orderId);

  const checks = {
    // 检查BOM是否存在
    bomComplete: checkBOMExists(order.bom_id),
    // 检查物料库存
    materialAvailable: checkMaterialInventory(order.material_id, order.norms_id, order.need_num),
    // 检查部门产能
    departmentCapacity: checkDepartmentCapacity(order.department_id, order.start_time, order.end_time),
    // 检查工单状态
    orderStatus: order.state === 1 ? {status: 'OK'} : {status: 'ERROR', message: '工单状态不正确'}
  };

  return {
    canRelease: Object.values(checks).every(check => check.status === 'OK'),
    warnings: Object.values(checks).filter(check => check.status === 'WARNING'),
    errors: Object.values(checks).filter(check => check.status === 'ERROR')
  };
}
```

### 6.2 工单状态自动更新逻辑（基于现有系统）
```javascript
// 工序开工触发工单状态更新（基于erp_process_task表）
function onTaskStart(taskId) {
  const task = getTaskById(taskId);
  const order = getOrderByTaskId(taskId);

  // 更新工序任务状态为已开工
  updateTaskStatus(taskId, 2, {  // 2-已开工
    actual_start_date: new Date(),
    operator_id: getCurrentUser().id,
    operator_name: getCurrentUser().name
  });

  // 工单状态在审核通过后保持为3，通过工序进度体现生产状态
  // 不需要额外更新工单状态
}

// 所有工序完工触发工单完工
function onAllTasksComplete(workOrderId) {
  const completedTasks = getCompletedTasksByOrder(workOrderId);
  const totalTasks = getTotalTasksByOrder(workOrderId);

  if (completedTasks.length === totalTasks.length) {
    // 更新工单完工状态
    updateOrderCompletionStatus(workOrderId, {
      completion_state: 1,  // 已完工
      state: 5  // 已完成
    });
  }
}

// 工单入库操作
function onOrderWarehouse(workOrderId) {
  updateOrderStatus(workOrderId, {
    state: 6  // 完工入库
  });
}
```

### 6.3 工单拆分业务逻辑（基于现有系统）
```javascript
// 工单拆分核心逻辑（基于erp_machin_header表）
function splitWorkOrder(parentOrderId, splitPlan) {
  // 1. 验证拆分条件
  const parentOrder = getOrderById(parentOrderId);
  if (parentOrder.state !== 3) {  // 只有审核通过的工单才能拆分
    throw new Error('只有审核通过的工单才能拆分');
  }

  // 2. 标记父工单为已拆分
  updateParentOrderSplitStatus(parentOrderId, {
    is_split_parent: 1,
    split_reason: splitPlan.reason,
    split_time: new Date(),
    split_by: getCurrentUser().id,
    original_quantity: parentOrder.need_num
  });

  // 3. 创建子工单
  const childOrders = [];
  for (let i = 0; i < splitPlan.children.length; i++) {
    const childPlan = splitPlan.children[i];
    const childOrder = createChildOrder(parentOrder, childPlan, i + 1);
    childOrders.push(childOrder);

    // 4. 复制工序任务到子工单
    copyProcessTasksToChildOrder(parentOrderId, childOrder.id, childPlan.quantity);

    // 5. 分配物料到子工单
    allocateMaterialsToChildOrder(parentOrderId, childOrder.id, childPlan.quantity);
  }

  // 6. 处理在制品分配
  allocateWIPToChildOrders(parentOrderId, childOrders);

  return childOrders;
}

// 创建子工单
function createChildOrder(parentOrder, childPlan, sequence) {
  const childOrderNum = `${parentOrder.order_num}-${sequence.toString().padStart(2, '0')}`;

  return insertChildOrder({
    order_num: childOrderNum,
    production_id: parentOrder.production_id,
    department_id: parentOrder.department_id,
    bar_code: generateBarCode(childOrderNum),
    material_id: parentOrder.material_id,
    norms_id: parentOrder.norms_id,
    need_num: childPlan.quantity,
    start_time: parentOrder.start_time,
    end_time: childPlan.delivery_date,
    state: 1,  // 新建状态
    pick_state: 1,  // 未领料
    sales_order_num: parentOrder.sales_order_num,
    sub_order_num: parentOrder.sub_order_num,
    bom_id: parentOrder.bom_id,
    parent_order_id: parentOrder.id,
    remark: `拆分子工单-${childPlan.remark}`,
    create_id: getCurrentUser().id,
    create_time: new Date()
  });
}
```

## 7. 页面交互说明

### 7.1 工单列表页面交互
- **筛选功能**：支持按状态、优先级、车间、时间范围等多维度筛选
- **批量操作**：支持批量下达、批量取消等操作
- **快速操作**：列表中直接提供下达、查看、暂停等快捷按钮
- **状态指示**：用颜色和图标直观显示工单状态和优先级

### 7.2 工单详情页面交互
- **实时更新**：工序进度、物料状态等信息实时刷新
- **关联查看**：可直接跳转到相关工序任务、物料单据等
- **操作权限**：根据用户角色显示可执行的操作按钮
- **历史追溯**：完整记录工单的所有操作历史

### 7.3 工单拆分页面交互
- **智能提示**：根据当前生产状态智能推荐拆分方案
- **影响预览**：实时计算拆分对交期、成本、资源的影响
- **风险提醒**：高亮显示拆分可能带来的风险和注意事项
- **审批流程**：集成审批流程，确保拆分操作的合规性
