# WIP管理页面设计（在制品管理）

## 1. 在制品库存查询

### 1.1 在制品库存列表
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📦 在制品库存查询                                                [导出]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 工单号：[_______] 产品：[_______] 当前工序：[全部▼] 状态：[全部▼]         │
│ 存放位置：[全部▼] 批次号：[_______] 日期：[本周▼]      [搜索] [重置]       │
├─────────────────────────────────────────────────────────────────────────────┤
│ 在制品库存列表                                                              │
│ ☐ 工单号      产品名称  规格    当前工序  下道工序  数量  状态    存放位置  │
│ ☑ MO240115001 轴承座    Φ50×100 精加工    装配      95   🟢在制   精加工区  │
│ ☐ MO240115002 法兰盘    Φ80×20  委外      精整      50   🟡委外中 委外厂商  │
│ ☐ MO240115003 连接件    M12×50  质检      入库      200  🔴待转序 质检区    │
│ ☐ MO240115004 支架      L型      装配      包装      80   🟢在制   装配区    │
│ ☐ MO240115005 底座      方形     粗加工    精加工    120  🟢在制   粗加工区  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 库存统计                                                                    │
│ 总在制品：545件  在制：395件  待转序：100件  委外中：50件                  │
│ 工序分布：粗加工120件  精加工95件  装配80件  质检200件  委外50件            │
│ 存放位置：粗加工区120件  精加工区95件  装配区80件  质检区200件  委外50件    │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 在制品详情查看
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 在制品详情 - MO240115001                                         [关闭] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单号：MO240115001  产品：轴承座  规格：Φ50×100                          │
│ 物料编码：MAT001  批次号：B240115001  生产日期：2024-01-15                 │
│ 当前数量：95件  单位：件  状态：🟢在制                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序信息                                                                    │
│ 当前工序：精加工  工序编码：OP003  工序序号：3                             │
│ 下道工序：装配  下道工序编码：OP004  工序序号：4                           │
│ 存放位置：精加工区  具体位置：A区-3号工位                                  │
│ 负责人：张师傅  联系方式：13800138001                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转历史                                                                    │
│ 时间              来源工序    目标工序    数量    操作人    备注            │
│ 2024-01-15 09:00  下料       粗加工      100     李师傅    首次投产        │
│ 2024-01-15 14:30  粗加工     精加工      100     王师傅    全部流转        │
│ 2024-01-15 16:00  精加工     质检        5       张师傅    不合格品        │
│ 2024-01-15 17:00  质检       精加工      0       质检员    返工处理        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. 在制品流转记录查询

### 2.1 流转记录列表
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔄 在制品流转记录查询                                            [导出]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 流转日期：[今日▼] 来源工序：[全部▼] 目标工序：[全部▼]                    │
│ 工单号：[_______] 流转人：[全部▼] 状态：[全部▼]       [搜索] [重置]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转记录列表                                                                │
│ 流转单号      工单号      产品    来源工序  目标工序  数量  状态    流转人  │
│ ZX240115001  MO240115001 轴承座  精加工    装配      50   ✅已完成  张师傅  │
│ ZX240115002  MO240115002 法兰盘  粗加工    精加工    30   ✅已完成  王师傅  │
│ ZX240115003  MO240115003 连接件  装配      质检      200  ✅已完成  李师傅  │
│ ZX240115004  MO240115004 支架    质检      入库      80   ✅已完成  质检员  │
│ ZX240115005  MO240115005 底座    下料      粗加工    120  ✅已完成  刘师傅  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转统计                                                                    │
│ 今日流转：8次  平均流转时间：2.3小时  最长等待：4.5小时                   │
│ 工序流转分布：下料→粗加工(2次)  粗加工→精加工(2次)  精加工→装配(2次)      │
│ 委外流转：发料1次  收货1次                                                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 流转记录详情
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 流转记录详情 - ZX240115001                                       [关闭] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 流转单号：ZX240115001  流转日期：2024-01-15 14:30                         │
│ 工单号：MO240115001  产品：轴承座  规格：Φ50×100                          │
│ 流转人：张师傅  联系方式：13800138001                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转信息                                                                    │
│ 来源工序：精加工  工序编码：OP003  来源位置：精加工区                      │
│ 目标工序：装配    工序编码：OP004  目标位置：装配区                        │
│ 流转数量：50件  单位：件  批次号：B240115001                               │
│ 流转状态：✅已完成  完成时间：2024-01-15 15:15                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 质检信息                                                                    │
│ 质检要求：是  质检类型：工序检验  质检员：李质检                           │
│ 质检时间：2024-01-15 14:45-15:00  质检结果：合格                          │
│ 检验项目：外观检查✅  尺寸检验✅  表面质量✅                               │
│ 质检备注：产品质量良好，符合装配要求                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 接收信息                                                                    │
│ 接收人：王师傅  接收时间：2024-01-15 15:15                                 │
│ 接收位置：装配区-B区  接收数量：50件                                       │
│ 接收备注：产品状态良好，已开始装配作业                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 时间轨迹                                                                    │
│ 14:30  张师傅    发出确认    50件产品发出，质量良好                        │
│ 14:45  李质检    开始质检    开始工序检验                                  │
│ 15:00  李质检    质检完成    50件全部合格                                  │
│ 15:15  王师傅    确认接收    50件产品接收完成                              │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 3. 在制品统计分析

### 3.1 在制品统计看板
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📊 在制品统计看板                                       [详细报告] [导出]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 时间范围：[本周▼] 2024年1月15日-21日  统计维度：[工序▼]                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 在制品概览                                                                  │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┐                   │
│ │ 总在制品    │ 在制数量    │ 待转序数量  │ 委外数量    │                   │
│ │    545件    │    395件    │    100件    │    50件     │                   │
│ └─────────────┴─────────────┴─────────────┴─────────────┘                   │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┐                   │
│ │ 在制品价值  │ 平均周转天数│ 流转次数    │ 流转效率    │                   │
│ │  24.8万元   │   3.2天     │    28次     │   92.5%     │                   │
│ └─────────────┴─────────────┴─────────────┴─────────────┘                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序在制品分布                                                              │
│ 工序名称    在制数量  占比    平均停留时间  流转频次  瓶颈指数              │
│ 下料        0件      0%      0.5天        高        ✅正常                │
│ 粗加工      120件    22%     1.2天        中        🟡关注                │
│ 精加工      95件     17%     2.1天        低        🔴瓶颈                │
│ 装配        80件     15%     1.8天        中        🟡关注                │
│ 质检        200件    37%     0.8天        高        ✅正常                │
│ 委外        50件     9%      3.5天        低        🔴瓶颈                │
├─────────────────────────────────────────────────────────────────────────────┤
│ 位置分布统计                                                                │
│ 存放位置      数量    占比    利用率    容量状态                            │
│ 粗加工区      120件   22%     85%      🟡接近满载                          │
│ 精加工区      95件    17%     76%      ✅正常                              │
│ 装配区        80件    15%     67%      ✅正常                              │
│ 质检区        200件   37%     95%      🔴接近满载                          │
│ 委外厂商      50件    9%      --       --                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 趋势分析                                                                    │
│ 本周趋势：在制品总量上升8%，主要集中在质检工序                             │
│ 瓶颈分析：精加工工序停留时间过长，建议增加设备或人员                       │
│ 改善建议：优化质检流程，提高质检效率，减少在制品积压                       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 在制品流转分析
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔄 在制品流转分析                                       [详细分析] [导出]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 分析维度：[工序流转▼] 时间范围：[本周▼]                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转效率分析                                                                │
│ 流转路径              流转次数  平均时间  最长时间  效率评级                │
│ 下料 → 粗加工         8次      0.5小时   1.2小时   ✅优秀                │
│ 粗加工 → 精加工       6次      2.1小时   4.5小时   🟡一般                │
│ 精加工 → 装配         5次      1.8小时   3.2小时   🟡一般                │
│ 装配 → 质检           7次      0.8小时   1.5小时   ✅良好                │
│ 质检 → 入库           4次      1.2小时   2.8小时   ✅良好                │
│ 精加工 → 委外         2次      3.5小时   6.0小时   🔴较差                │
│ 委外 → 精整           1次      24小时    24小时    🔴较差                │
├─────────────────────────────────────────────────────────────────────────────┤
│ 异常流转分析                                                                │
│ 异常类型        发生次数  占比    主要原因              改善建议            │
│ 超时流转        3次      10.7%   设备故障、人员不足    增加备用设备        │
│ 质量退回        2次      7.1%    工艺问题、操作失误    加强培训            │
│ 位置错误        1次      3.6%    标识不清、沟通不畅    完善标识系统        │
│ 数量差异        1次      3.6%    计数错误、遗漏        双人复核            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 改善建议                                                                    │
│ 1. 精加工工序：增加1台设备，预计可减少50%等待时间                          │
│ 2. 委外管理：建立委外厂商考核机制，要求24小时内完成                        │
│ 3. 质检流程：实施并行质检，减少质检等待时间                                 │
│ 4. 流转标识：统一在制品标识，减少流转错误                                   │
│ 5. 系统优化：实时更新在制品位置，提高数据准确性                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. 数据库字段映射

### 4.1 在制品库存表 (erp_wip_inventory)
```sql
-- 在制品库存查询主要字段
SELECT
    wip.wip_id,                    -- 在制品ID
    wip.production_order_id,       -- 工单号
    wip.material_id,               -- 物料ID
    wip.current_operation_id,      -- 当前工序ID
    wip.next_operation_id,         -- 下道工序ID
    wip.quantity,                  -- 数量
    wip.status,                    -- 状态(在制、待转序、已转序、委外中)
    wip.location,                  -- 存放位置
    wip.batch_no,                  -- 批次号
    wip.create_time,               -- 创建时间
    wip.update_time,               -- 更新时间
    mo.material_name,              -- 产品名称
    mo.specification,              -- 规格
    op1.operation_name as current_op_name,  -- 当前工序名称
    op2.operation_name as next_op_name      -- 下道工序名称
FROM erp_wip_inventory wip
LEFT JOIN erp_machin_header mo ON wip.production_order_id = mo.machin_id
LEFT JOIN erp_operation op1 ON wip.current_operation_id = op1.operation_id
LEFT JOIN erp_operation op2 ON wip.next_operation_id = op2.operation_id
WHERE wip.status IN ('在制', '待转序', '已转序', '委外中')
ORDER BY wip.create_time DESC;
```

### 4.2 在制品流转表 (erp_wip_transfer)
```sql
-- 在制品流转记录查询主要字段
SELECT
    wt.transfer_id,                -- 流转ID
    wt.transfer_code,              -- 流转单号
    wt.production_order_id,        -- 工单号
    wt.material_id,                -- 物料ID
    wt.from_operation_id,          -- 来源工序ID
    wt.to_operation_id,            -- 目标工序ID
    wt.quantity,                   -- 流转数量
    wt.transfer_time,              -- 流转时间
    wt.transfer_by,                -- 流转人
    wt.status,                     -- 状态(已流转)
    wt.quality_check_required,     -- 是否需要质检
    wt.quality_check_result,       -- 质检结果
    wt.notes,                      -- 备注
    mo.material_name,              -- 产品名称
    mo.specification,              -- 规格
    op1.operation_name as from_op_name,    -- 来源工序名称
    op2.operation_name as to_op_name,      -- 目标工序名称
    u.user_name as transfer_user_name      -- 流转人姓名
FROM erp_wip_transfer wt
LEFT JOIN erp_machin_header mo ON wt.production_order_id = mo.machin_id
LEFT JOIN erp_operation op1 ON wt.from_operation_id = op1.operation_id
LEFT JOIN erp_operation op2 ON wt.to_operation_id = op2.operation_id
LEFT JOIN erp_user u ON wt.transfer_by = u.user_id
ORDER BY wt.transfer_time DESC;
```

## 5. 业务规则说明

### 5.1 在制品状态管理规则
```
在制品状态流转：
在制 → 待转序 → 已转序 → 在制（下道工序）

特殊状态：
- 委外中：委外工序专用状态
- 不合格：质检不合格状态
- 返工中：不合格品返工状态
- 报废：不可修复的废品状态
```

### 5.2 流转控制规则
```
流转前置条件：
1. 上道工序必须完工（部分完工或全部完工）
2. 下道工序必须具备开工条件
3. 质检点工序必须通过质检
4. 委外工序必须完成收货确认

流转数量控制：
- 流转数量 ≤ 当前工序可流转数量
- 下道工序接收数量 = 流转数量 - 质检不合格数量
- 支持部分流转，剩余数量继续在当前工序
```

### 5.3 库存一致性规则
```
数据一致性检查：
1. 各工序在制品数量总和 = 工单投产数量 - 完工入库数量 - 报废数量
2. 流转记录的累计数量 = 各工序接收数量
3. 盘点差异超过阈值（5%）需要审批确认
4. 系统自动校验在制品数量的合理性
```

## 6. 页面交互说明

### 6.1 在制品库存查询
- 支持多条件组合筛选，实时查询在制品状态
- 点击工单号可查看工单详情页面
- 点击在制品记录可查看详细流转历史
- 支持导出Excel格式数据，包含完整在制品信息
- 页面数据每30秒自动刷新，确保数据实时性

### 6.2 流转记录查询
- 按时间、工序、工单等维度筛选流转记录
- 显示完整的流转轨迹和时间节点
- 支持流转异常的标识和处理状态查看
- 提供流转效率统计分析和瓶颈识别
- 关联显示质检结果和报工记录

### 6.3 统计分析功能
- 实时统计在制品分布和流转效率
- 提供可视化图表展示趋势变化
- 支持多维度分析（工序、位置、时间等）
- 自动识别生产瓶颈和异常情况
- 生成改善建议和优化方案

## 7. 功能实现要点

### 7.1 查询性能优化
```sql
-- 在制品库存查询索引
CREATE INDEX idx_wip_inventory_status ON erp_wip_inventory(status, current_operation_id);
CREATE INDEX idx_wip_inventory_order ON erp_wip_inventory(production_order_id);
CREATE INDEX idx_wip_transfer_time ON erp_wip_transfer(transfer_time);
CREATE INDEX idx_wip_inventory_location ON erp_wip_inventory(location);
```

### 7.2 实时数据更新
- 报工完成时自动更新在制品状态
- 质检完成时自动触发流转记录创建
- 委外收发货时同步更新在制品位置
- 工序任务状态变化时联动更新在制品数据
- 使用数据库触发器确保数据一致性

### 7.3 数据校验规则
- 在制品数量不能为负数
- 流转数量不能超过当前库存数量
- 状态变更必须符合业务流程规则
- 位置信息必须与工序设置匹配
- 自动校验在制品数量的合理性

### 7.4 异常处理机制
- 数据异常自动预警和提示
- 流转超时自动标识和跟踪
- 库存差异自动检测和报告
- 系统故障时的数据恢复机制
- 操作日志完整记录和审计

---

**设计说明**：
- 严格按照业务流程设计文档中的WIP管理流程设计
- 完全对应 erp_wip_inventory 和 erp_wip_transfer 表结构
- 专注于查询、监控和统计分析功能
- 实现自动流转机制，无需手动调拨和盘点
- 提供实时的在制品状态跟踪和异常预警
- 支持多维度的在制品分析和报表功能

## 8. API接口设计

### 8.1 在制品库存列表查询接口

**接口地址：** `GET /api/production/wip-inventory`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "materialId": "",                  // 物料ID
  "normsId": "",                     // 规格ID
  "productionOrderId": "",           // 生产工单ID
  "currentOperationId": "",          // 当前工序ID
  "nextOperationId": "",             // 下一工序ID
  "status": "",                      // 状态：在制/待转序/已转序/委外中/不合格
  "location": "",                    // 存放位置（模糊查询）
  "batchNo": "",                     // 批次号（模糊查询）
  "productionDateBegin": "",         // 生产日期-开始
  "productionDateEnd": "",           // 生产日期-结束
  "createTimeBegin": "",             // 创建时间-开始
  "createTimeEnd": "",               // 创建时间-结束
  "departmentId": "",                // 部门ID（可选）
  "materialName": "",                // 物料名称（模糊查询）
  "orderNum": ""                     // 工单编号（模糊查询）
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 150,
    "list": [
      {
        "id": "WIP240115001",
        "materialId": "MAT001",
        "normsId": "NORM001",
        "productionOrderId": "WO240115001",
        "currentOperationId": "OP002",
        "nextOperationId": "OP003",
        "quantity": 50.00,
        "unit": "件",
        "status": "在制",
        "location": "机加工车间-工位02",
        "batchNo": "B240115001",
        "productionDate": "2024-01-15",
        "remarks": "",
        "createTime": "2024-01-15 08:00:00",
        "updateTime": "2024-01-15 12:30:00",
        // 关联信息
        "materialName": "轴承座",
        "materialSpec": "φ50×100",
        "orderNum": "MO240115001",
        "currentOperationName": "粗加工",
        "nextOperationName": "精加工",
        "statusName": "在制",
        "departmentName": "机加工车间",
        "equipmentName": "数控车床02",
        "operatorName": "张师傅",
        "progressRate": 40.0,
        "stayDays": 2
      }
    ]
  }
}
```

### 8.2 在制品库存详情查询接口

**接口地址：** `GET /api/production/wip-inventory/{id}`

**路径参数：**
- `id`: 在制品记录ID

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "WIP240115001",
    "materialId": "MAT001",
    "normsId": "NORM001",
    "productionOrderId": "WO240115001",
    "currentOperationId": "OP002",
    "nextOperationId": "OP003",
    "quantity": 50.00,
    "unit": "件",
    "status": "在制",
    "location": "机加工车间-工位02",
    "batchNo": "B240115001",
    "productionDate": "2024-01-15",
    "remarks": "",
    "createTime": "2024-01-15 08:00:00",
    "updateTime": "2024-01-15 12:30:00",
    // 关联信息
    "materialInfo": {
      "materialName": "轴承座",
      "materialSpec": "φ50×100",
      "materialType": "成品",
      "unit": "件"
    },
    "orderInfo": {
      "orderNum": "MO240115001",
      "planQty": 100.00,
      "completedQty": 40.00,
      "remainingQty": 60.00,
      "deliveryDate": "2024-01-25"
    },
    "processInfo": {
      "currentOperationName": "粗加工",
      "nextOperationName": "精加工",
      "departmentName": "机加工车间",
      "equipmentName": "数控车床02",
      "operatorName": "张师傅"
    },
    "transferHistory": [
      {
        "transferCode": "ZX240115001",
        "fromOperationName": "下料",
        "toOperationName": "粗加工",
        "quantity": 50.00,
        "transferTime": "2024-01-15 08:00:00",
        "transferBy": "李师傅"
      }
    ]
  }
}
```

### 8.3 在制品流转记录列表查询接口

**接口地址：** `GET /api/production/wip-transfers`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "transferCode": "",                // 流转单号（模糊查询）
  "productionOrderId": "",           // 生产工单ID
  "materialId": "",                  // 物料ID
  "fromOperationId": "",             // 来源工序ID
  "toOperationId": "",               // 目标工序ID
  "fromTaskId": "",                  // 来源任务ID
  "toTaskId": "",                    // 目标任务ID
  "transferBy": "",                  // 流转人ID
  "status": "",                      // 状态
  "batchNo": "",                     // 批次号（模糊查询）
  "transferTimeBegin": "",           // 流转时间-开始
  "transferTimeEnd": "",             // 流转时间-结束
  "createTimeBegin": "",             // 创建时间-开始
  "createTimeEnd": "",               // 创建时间-结束
  "departmentId": "",                // 部门ID（可选）
  "materialName": "",                // 物料名称（模糊查询）
  "orderNum": ""                     // 工单编号（模糊查询）
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 80,
    "list": [
      {
        "id": "TF240115001",
        "transferCode": "ZX240115001",
        "productionOrderId": "WO240115001",
        "materialId": "MAT001",
        "fromOperationId": "OP001",
        "toOperationId": "OP002",
        "fromTaskId": "TASK240115001",
        "toTaskId": "TASK240115002",
        "quantity": 50.00,
        "unit": "件",
        "batchNo": "B240115001",
        "transferTime": "2024-01-15 12:30:00",
        "transferBy": "OP001",
        "status": "已流转",
        "remarks": "",
        "createTime": "2024-01-15 12:30:00",
        // 关联信息
        "materialName": "轴承座",
        "orderNum": "MO240115001",
        "fromOperationName": "下料",
        "toOperationName": "粗加工",
        "transferByName": "张师傅",
        "statusName": "已流转",
        "fromDepartmentName": "下料车间",
        "toDepartmentName": "机加工车间"
      }
    ]
  }
}
```

### 8.4 在制品流转记录详情查询接口

**接口地址：** `GET /api/production/wip-transfers/{id}`

**路径参数：**
- `id`: 流转记录ID

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "TF240115001",
    "transferCode": "ZX240115001",
    "productionOrderId": "WO240115001",
    "materialId": "MAT001",
    "fromOperationId": "OP001",
    "toOperationId": "OP002",
    "fromTaskId": "TASK240115001",
    "toTaskId": "TASK240115002",
    "quantity": 50.00,
    "unit": "件",
    "batchNo": "B240115001",
    "transferTime": "2024-01-15 12:30:00",
    "transferBy": "OP001",
    "status": "已流转",
    "remarks": "",
    "createTime": "2024-01-15 12:30:00",
    // 关联信息
    "materialInfo": {
      "materialName": "轴承座",
      "materialSpec": "φ50×100",
      "materialType": "成品"
    },
    "orderInfo": {
      "orderNum": "MO240115001",
      "planQty": 100.00,
      "deliveryDate": "2024-01-25"
    },
    "fromOperationInfo": {
      "operationName": "下料",
      "departmentName": "下料车间",
      "equipmentName": "数控切割机"
    },
    "toOperationInfo": {
      "operationName": "粗加工",
      "departmentName": "机加工车间",
      "equipmentName": "数控车床02"
    },
    "transferByInfo": {
      "operatorName": "张师傅",
      "departmentName": "下料车间"
    }
  }
}
```

### 8.5 在制品统计分析接口

**接口地址：** `GET /api/production/wip-statistics`

**请求参数：**
```json
{
  "dateType": "week",                // 统计周期：day/week/month/year
  "startDate": "2024-01-15",
  "endDate": "2024-01-21",
  "departmentId": "",                // 部门ID（可选）
  "materialId": "",                  // 物料ID（可选）
  "operationId": "",                 // 工序ID（可选）
  "status": ""                       // 状态（可选）
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "summary": {
      "totalWipQty": 2580.00,
      "totalWipValue": 1290000.00,
      "avgStayDays": 3.2,
      "transferCount": 156,
      "exceptionCount": 8,
      "wipTurnoverRate": 12.5
    },
    "statusDistribution": [
      {"status": "在制", "statusName": "在制", "qty": 1200.00, "percentage": 46.5},
      {"status": "待转序", "statusName": "待转序", "qty": 800.00, "percentage": 31.0},
      {"status": "已转序", "statusName": "已转序", "qty": 480.00, "percentage": 18.6},
      {"status": "委外中", "statusName": "委外中", "qty": 100.00, "percentage": 3.9}
    ],
    "departmentDistribution": [
      {"departmentName": "机加工车间", "wipQty": 1200.00, "wipValue": 600000.00, "percentage": 46.5},
      {"departmentName": "装配车间", "wipQty": 800.00, "wipValue": 400000.00, "percentage": 31.0},
      {"departmentName": "表面处理", "wipQty": 580.00, "wipValue": 290000.00, "percentage": 22.5}
    ],
    "operationDistribution": [
      {"operationName": "粗加工", "wipQty": 600.00, "avgStayDays": 2.5},
      {"operationName": "精加工", "wipQty": 500.00, "avgStayDays": 3.8},
      {"operationName": "装配", "wipQty": 400.00, "avgStayDays": 4.2}
    ],
    "transferTrend": [
      {"date": "2024-01-15", "transferCount": 22, "transferQty": 1100.00},
      {"date": "2024-01-16", "transferCount": 25, "transferQty": 1250.00},
      {"date": "2024-01-17", "transferCount": 28, "transferQty": 1400.00}
    ],
    "exceptionAnalysis": [
      {"exceptionType": "流转超时", "count": 3, "avgDelay": 2.5},
      {"exceptionType": "库存异常", "count": 2, "avgDelay": 1.8},
      {"exceptionType": "位置错误", "count": 3, "avgDelay": 0.5}
    ]
  }
}
```

### 8.6 在制品异常预警接口

**接口地址：** `GET /api/production/wip-alerts`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "alertType": "",                   // 预警类型：stay_timeout/qty_exception/location_error/transfer_timeout
  "level": "",                       // 预警级别：low/medium/high/critical
  "status": "",                      // 处理状态：pending/processing/resolved
  "departmentId": "",                // 部门ID（可选）
  "createTimeBegin": "",             // 创建时间-开始
  "createTimeEnd": ""                // 创建时间-结束
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 15,
    "list": [
      {
        "id": "ALERT240115001",
        "alertType": "stay_timeout",
        "alertTypeName": "停留超时",
        "level": "high",
        "levelName": "高",
        "wipId": "WIP240115001",
        "materialName": "轴承座",
        "orderNum": "MO240115001",
        "currentOperationName": "粗加工",
        "location": "机加工车间-工位02",
        "stayDays": 5,
        "threshold": 3,
        "quantity": 50.00,
        "alertMessage": "在制品在粗加工工序停留已超过5天，超出标准停留时间3天",
        "status": "pending",
        "statusName": "待处理",
        "createTime": "2024-01-15 14:30:00",
        "updateTime": "2024-01-15 14:30:00"
      }
    ]
  }
}
```

### 8.7 在制品流转轨迹查询接口

**接口地址：** `GET /api/production/wip-trace/{wipId}`

**路径参数：**
- `wipId`: 在制品记录ID

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "wipInfo": {
      "id": "WIP240115001",
      "materialName": "轴承座",
      "orderNum": "MO240115001",
      "batchNo": "B240115001",
      "currentStatus": "在制",
      "currentOperationName": "粗加工",
      "totalStayDays": 5
    },
    "traceHistory": [
      {
        "operationName": "下料",
        "departmentName": "下料车间",
        "startTime": "2024-01-10 08:00:00",
        "endTime": "2024-01-10 16:00:00",
        "stayHours": 8.0,
        "quantity": 50.00,
        "operatorName": "王师傅",
        "status": "已完成",
        "transferCode": null
      },
      {
        "operationName": "流转",
        "departmentName": "下料车间→机加工车间",
        "startTime": "2024-01-10 16:00:00",
        "endTime": "2024-01-11 08:00:00",
        "stayHours": 16.0,
        "quantity": 50.00,
        "operatorName": "张师傅",
        "status": "已流转",
        "transferCode": "ZX240110001"
      },
      {
        "operationName": "粗加工",
        "departmentName": "机加工车间",
        "startTime": "2024-01-11 08:00:00",
        "endTime": null,
        "stayHours": 96.0,
        "quantity": 50.00,
        "operatorName": "李师傅",
        "status": "进行中",
        "transferCode": null
      }
    ]
  }
}
```

### 8.8 在制品数据导出接口

**接口地址：** `POST /api/production/wip-export`

**请求参数：**
```json
{
  "exportType": "inventory",         // 导出类型：inventory-库存，transfer-流转记录，statistics-统计报表
  "fileFormat": "excel",             // 文件格式：excel/pdf
  "filters": {                       // 筛选条件（与列表查询相同）
    "status": "在制",
    "departmentId": "DEPT001",
    "productionDateBegin": "2024-01-01",
    "productionDateEnd": "2024-01-31"
  },
  "includeDetails": true,            // 是否包含详细信息
  "includeTrace": false              // 是否包含流转轨迹
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "downloadUrl": "/api/files/download/wip-inventory-20240115.xlsx",
    "fileName": "在制品库存报表-20240115.xlsx",
    "fileSize": 3560000
  }
}
```

### 8.9 在制品看板数据接口

**接口地址：** `GET /api/production/wip-dashboard`

**请求参数：**
```json
{
  "departmentId": "",                // 部门ID（可选）
  "refreshInterval": 30              // 刷新间隔（秒）
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "realTimeData": {
      "totalWipQty": 2580.00,
      "totalWipValue": 1290000.00,
      "todayTransferCount": 45,
      "pendingAlertCount": 8,
      "avgStayDays": 3.2,
      "wipTurnoverRate": 12.5
    },
    "statusChart": [
      {"status": "在制", "qty": 1200.00, "color": "#1890ff"},
      {"status": "待转序", "qty": 800.00, "color": "#faad14"},
      {"status": "已转序", "qty": 480.00, "color": "#52c41a"},
      {"status": "委外中", "qty": 100.00, "color": "#722ed1"}
    ],
    "departmentChart": [
      {"departmentName": "机加工车间", "wipQty": 1200.00, "percentage": 46.5},
      {"departmentName": "装配车间", "wipQty": 800.00, "percentage": 31.0},
      {"departmentName": "表面处理", "wipQty": 580.00, "percentage": 22.5}
    ],
    "transferTrendChart": [
      {"time": "08:00", "transferCount": 5},
      {"time": "10:00", "transferCount": 8},
      {"time": "12:00", "transferCount": 12},
      {"time": "14:00", "transferCount": 15},
      {"time": "16:00", "transferCount": 10}
    ],
    "alertList": [
      {
        "alertType": "stay_timeout",
        "materialName": "轴承座",
        "operationName": "粗加工",
        "stayDays": 5,
        "level": "high"
      }
    ],
    "topMaterials": [
      {"materialName": "轴承座", "wipQty": 500.00, "wipValue": 250000.00},
      {"materialName": "连接件", "wipQty": 400.00, "wipValue": 200000.00},
      {"materialName": "支撑架", "wipQty": 300.00, "wipValue": 150000.00}
    ]
  }
}
```

### 8.10 在制品批量操作接口

**接口地址：** `POST /api/production/wip-batch-operation`

**请求参数：**
```json
{
  "operation": "update_location",    // 操作类型：update_location-更新位置，update_status-更新状态
  "wipIds": ["WIP001", "WIP002"],    // 在制品ID列表
  "params": {                        // 操作参数
    "location": "机加工车间-工位03",  // 新位置（update_location时）
    "status": "待转序",              // 新状态（update_status时）
    "remarks": "批量位置调整"        // 备注
  }
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "批量操作成功",
  "data": {
    "successCount": 2,
    "failCount": 0,
    "results": [
      {
        "wipId": "WIP001",
        "success": true,
        "message": "位置更新成功"
      },
      {
        "wipId": "WIP002",
        "success": true,
        "message": "位置更新成功"
      }
    ]
  }
}
```
