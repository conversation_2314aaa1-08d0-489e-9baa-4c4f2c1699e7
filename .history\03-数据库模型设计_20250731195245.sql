-- =====================================================
-- ERP生产管理系统数据库模型设计
-- 适用于机械加工行业（五金加工件）
-- 支持按单生产、多工序、委外加工
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 生产工单管理模块
-- =====================================================

-- 1.1 生产工单主表
CREATE TABLE `erp_machin_header` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `order_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单号',
  `production_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产计划单id',
  `department_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门id',
  `bar_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '条形码路径',
  `material_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '加工的成品id',
  `norms_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '加工的成品规格id',
  `need_num` decimal(24,6) NOT NULL COMMENT '需要加工的数量',
  `start_time` datetime NOT NULL COMMENT '计划开始加工时间',
  `end_time` datetime NOT NULL COMMENT '计划完成加工时间',
  `state` int(2) NOT NULL DEFAULT '1' COMMENT '状态  1.新建  2.审核中  3.审核通过  4.审核拒绝  5.已完成  6.完工入库',
  `execution_state` int(2) NOT NULL DEFAULT '1' COMMENT '工单执行状态  1.待下达  2.已下达  3.生产中  4.已完工  5.已入库  6.已关闭',
  `pick_state` int(2) NOT NULL DEFAULT '1' COMMENT '领料状态  1.未领料  2.已领料',
  `examine_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核人',
  `examine_content` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核意见',
  `examine_time` datetime DEFAULT NULL COMMENT '审核时间',
  `enclosure_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '附件id',
  `remark` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `sales_order_num` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sub_order_num` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `bom_id` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `oper_time` datetime DEFAULT NULL COMMENT '单据时间',
  `completion_state` int(11) NOT NULL DEFAULT '0' COMMENT '完工状态 0 未完工  1已完工',
  `process_instance_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'submit_type=1走工作流提交时，工作流程id',
  `submit_type` int(11) NOT NULL DEFAULT '2' COMMENT '单据提交类型  1.走工作流提交  2.直接提交',
  `version_num` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，处理数据幂等性问题',
  `parent_order_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父工单ID（拆分子工单时使用）',
  `is_split_parent` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为拆分父工单：0-否，1-是',
  `split_reason` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拆分原因',
  `split_time` datetime DEFAULT NULL COMMENT '拆分时间',
  `split_by` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拆分人ID',
  `original_quantity` decimal(24,6) DEFAULT NULL COMMENT '原始数量（拆分前数量）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='生产工单主单据表';

-- 1.2 工序任务表
CREATE TABLE erp_process_task (
    id VARCHAR(32) PRIMARY KEY COMMENT '任务ID',
    task_no VARCHAR(50) NOT NULL COMMENT '任务编号',
    work_order_id VARCHAR(32) NOT NULL COMMENT '生产工单ID',
    process_id VARCHAR(32) NOT NULL COMMENT '工序ID',
    process_code VARCHAR(50) NOT NULL COMMENT '工序编码',
    process_name VARCHAR(100) NOT NULL COMMENT '工序名称',
    sequence_no INT NOT NULL COMMENT '工序序号',
    is_outsource TINYINT NOT NULL DEFAULT 0 COMMENT '是否委外：0-内部，1-委外',
    is_quality_check TINYINT NOT NULL DEFAULT 0 COMMENT '是否质检点：0-否，1-是',
    plan_qty DECIMAL(10,3) NOT NULL COMMENT '计划数量',
    completed_qty DECIMAL(10,3) NOT NULL DEFAULT 0 COMMENT '完成数量',
    qualified_qty DECIMAL(10,3) NOT NULL DEFAULT 0 COMMENT '合格数量',
    unqualified_qty DECIMAL(10,3) NOT NULL DEFAULT 0 COMMENT '不合格数量',
    standard_hours DECIMAL(8,2) NOT NULL DEFAULT 0 COMMENT '标准工时（小时）',
    actual_hours DECIMAL(8,2) NOT NULL DEFAULT 0 COMMENT '实际工时（小时）',
    unit_price DECIMAL(10,4) NOT NULL DEFAULT 0 COMMENT '工序单价',
    total_cost DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '工序总成本',
    equipment_id VARCHAR(32) COMMENT '设备ID',
    equipment_name VARCHAR(100) COMMENT '设备名称',
    operator_id VARCHAR(32) COMMENT '操作工ID',
    operator_name VARCHAR(50) COMMENT '操作工姓名',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待开工，2-已开工，3-部分完工，4-全部完工，5-已暂停，6-已取消',
    plan_start_date DATE COMMENT '计划开始日期',
    plan_end_date DATE COMMENT '计划完成日期',
    actual_start_date DATE COMMENT '实际开始日期',
    actual_end_date DATE COMMENT '实际完成日期',
    prev_task_ids TEXT COMMENT '前置任务ID列表（JSON格式）',
    next_task_ids TEXT COMMENT '后置任务ID列表（JSON格式）',
    remark TEXT COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人ID',
    updated_by VARCHAR(32) COMMENT '更新人ID',
    
    UNIQUE KEY uk_task_no (task_no),
    INDEX idx_work_order_id (work_order_id),
    INDEX idx_process_id (process_id),
    INDEX idx_status (status),
    INDEX idx_operator_id (operator_id),
    INDEX idx_equipment_id (equipment_id),
    INDEX idx_plan_end_date (plan_end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工序任务表';

-- =====================================================
-- 2. 生产报工模块
-- =====================================================

-- 2.1 工序报工表
CREATE TABLE erp_work_report (
    id VARCHAR(32) PRIMARY KEY COMMENT '报工ID',
    report_no VARCHAR(50) NOT NULL COMMENT '报工单号',
    task_id VARCHAR(32) NOT NULL COMMENT '工序任务ID',
    work_order_id VARCHAR(32) NOT NULL COMMENT '生产工单ID',
    process_id VARCHAR(32) NOT NULL COMMENT '工序ID',
    operator_id VARCHAR(32) NOT NULL COMMENT '操作工ID',
    operator_name VARCHAR(50) NOT NULL COMMENT '操作工姓名',
    report_type TINYINT NOT NULL DEFAULT 1 COMMENT '报工类型：1-快速报工，2-异常报工，3-停机报工',
    completed_qty DECIMAL(10,3) NOT NULL DEFAULT 0 COMMENT '完成数量',
    qualified_qty DECIMAL(10,3) NOT NULL DEFAULT 0 COMMENT '合格数量',
    unqualified_qty DECIMAL(10,3) NOT NULL DEFAULT 0 COMMENT '不合格数量',
    work_hours DECIMAL(8,2) NOT NULL DEFAULT 0 COMMENT '工时（小时）',
    efficiency_rate DECIMAL(5,2) NOT NULL DEFAULT 0 COMMENT '效率（%）',
    start_time DATETIME NOT NULL COMMENT '开工时间',
    end_time DATETIME NOT NULL COMMENT '完工时间',
    exception_type VARCHAR(20) COMMENT '异常类型：设备故障、质量问题、物料短缺、其他',
    exception_desc TEXT COMMENT '异常描述',
    downtime_reason VARCHAR(100) COMMENT '停机原因',
    downtime_duration DECIMAL(8,2) DEFAULT 0 COMMENT '停机时长（小时）',
    photos JSON COMMENT '照片附件（JSON格式）',
    remark TEXT COMMENT '备注',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-已提交，2-已审核，3-已关闭',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人ID',
    updated_by VARCHAR(32) COMMENT '更新人ID',
    
    UNIQUE KEY uk_report_no (report_no),
    INDEX idx_task_id (task_id),
    INDEX idx_work_order_id (work_order_id),
    INDEX idx_operator_id (operator_id),
    INDEX idx_report_type (report_type),
    INDEX idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工序报工表';

-- =====================================================
-- 3. 委外管理模块
-- =====================================================

-- 3.1 委外工序表
CREATE TABLE erp_outsource_process (
    id VARCHAR(32) PRIMARY KEY COMMENT '委外ID',
    outsource_no VARCHAR(50) NOT NULL COMMENT '委外单号',
    work_order_id VARCHAR(32) NOT NULL COMMENT '生产工单ID',
    task_id VARCHAR(32) NOT NULL COMMENT '工序任务ID',
    process_id VARCHAR(32) NOT NULL COMMENT '工序ID',
    supplier_id VARCHAR(32) NOT NULL COMMENT '供应商ID',
    supplier_name VARCHAR(100) NOT NULL COMMENT '供应商名称',
    outsource_qty DECIMAL(10,3) NOT NULL COMMENT '委外数量',
    unit_price DECIMAL(10,4) NOT NULL DEFAULT 0 COMMENT '单价',
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0 COMMENT '总金额',
    delivery_date DATE COMMENT '要求交期',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待申请，2-已申请，3-已审核，4-已发料，5-生产中，6-待收货，7-已收货，8-已关闭',
    apply_date DATE COMMENT '申请日期',
    approve_date DATE COMMENT '审核日期',
    send_date DATE COMMENT '发料日期',
    receive_date DATE COMMENT '收货日期',
    received_qty DECIMAL(10,3) DEFAULT 0 COMMENT '收货数量',
    qualified_qty DECIMAL(10,3) DEFAULT 0 COMMENT '合格数量',
    unqualified_qty DECIMAL(10,3) DEFAULT 0 COMMENT '不合格数量',
    remark TEXT COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人ID',
    updated_by VARCHAR(32) COMMENT '更新人ID',
    
    UNIQUE KEY uk_outsource_no (outsource_no),
    INDEX idx_work_order_id (work_order_id),
    INDEX idx_task_id (task_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_status (status),
    INDEX idx_delivery_date (delivery_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='委外工序表';


-- =====================================================
-- 4. 物料管理模块
-- =====================================================

-- 4.1 物料单据表（统一领料、退料、补料）
CREATE TABLE `erp_pick_header` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `default_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单据编号',
  `depot_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '仓库',
  `type` int(2) NOT NULL COMMENT '单据类型  19.领料单  20.补料单  21.退料单',
  `oper_time` datetime NOT NULL COMMENT '单据时间',
  `total_price` decimal(24,6) DEFAULT NULL,
  `remark` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `department_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门id',
  `status` int(2) NOT NULL DEFAULT '0' COMMENT '状态  0未审核  1.审核中  2.审核通过-进行领料和退料  3.审核拒绝',
  `examine_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核人',
  `examine_content` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核意见',
  `examine_time` datetime DEFAULT NULL COMMENT '审核时间',
  `machin_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '加工单id',
  `production_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产计划单id',
  `enclosure_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '附件id',
  `create_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `real_complate_time` datetime DEFAULT NULL COMMENT '实际完成时间，审核通过后的时间,审核不通过不修改该时间',
  `fin_voucher_id` bigint(20) DEFAULT NULL COMMENT '生成的凭证id',
  `fin_step_status` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '对应财务系统单据状态：默认0  ， 1 完成结算凭证生成',
  `submit_type` int(11) NOT NULL DEFAULT '2' COMMENT '单据提交类型  1.走工作流提交  2.直接提交',
  `process_instance_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'submit_type=1走工作流提交时，工作流程id',
  `material_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品id',
  `work_order_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联生产工单ID',
  `task_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联工序任务ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='领料/退料/补料单主单据表';
-- 4.2 物料单据明细表
CREATE TABLE `erp_pick_child` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `header_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '父单据id',
  `department_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门id',
  `material_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品id',
  `norms_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格id',
  `need_num` decimal(24,6) NOT NULL COMMENT '领料/退料数量',
  `unit_price` decimal(24,6) DEFAULT NULL,
  `all_price` decimal(24,6) DEFAULT NULL,
  `remark` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `version_num` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，处理数据幂等性问题',
  `applicant` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '领料人/退料人',
  `batch_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批次id',
  `batch_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批次code',
  `batch_inventory_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批次子项id',
  `work_order_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联生产工单ID',
  `task_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联工序任务ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='领料/退料/补料单子单据表';

-- =====================================================
-- 5. 质量管理模块
-- =====================================================

-- 5.1 质检单表
CREATE TABLE `erp_quality_check` (
  `id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL,
  `number` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '单据编号',
  `supplier` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '供应商id',
  `check_type` char(1) COLLATE utf8mb4_general_ci NOT NULL COMMENT '单据类型：1 来料质检  2 验收入库',
  `depot_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '仓库id',
  `apply_staff` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '申请人',
  `apply_time` datetime NOT NULL COMMENT '申请检查时间',
  `check_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '检查状态: 0未检查 1进行中  2已检查 3已入库',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `submit_type` int(2) NOT NULL DEFAULT '2' COMMENT '单据提交类型 ： 1走流程 2直接提交',
  `process_instance_id` varchar(55) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流程实例id:submitType 为1 时需要实例id',
  `status` int(2) NOT NULL DEFAULT '0' COMMENT '0未审核、1.审核中、2.审核通过、3.审核拒绝、4.已转入库|出库，已完成',
  `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '默认 0正常 1删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `execution_status` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务执行状态：0未执行  1部分执行  2全部执行',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='报检单';

CREATE TABLE `erp_quality_check_item` (
  `id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL,
  `check_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主表单id',
  `material_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL,
  `norm_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品规格id',
  `qc_method` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '质检方式',
  `quantity` decimal(15,6) NOT NULL COMMENT '物料数量',
  `unit` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '单位',
  `spec` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规格',
  `head_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单据id',
  `check_quantity` decimal(15,6) DEFAULT NULL COMMENT '检查数量',
  `qualified_quantity` decimal(15,6) DEFAULT NULL COMMENT '合格数量',
  `sample_quantity` decimal(15,6) DEFAULT NULL COMMENT '抽样数量',
  `sample_qualified_quantity` decimal(15,6) DEFAULT NULL COMMENT '样品合格数量',
  `check_status` char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '检查状态：0 未检 1已检',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请检查时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成检查时间',
  `check_by` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '检查人',
  `currency_type` varchar(15) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '币别',
  `currency_rate` decimal(8,4) DEFAULT NULL COMMENT '汇率',
  `item_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单据子项id',
  `handle_method` char(1) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理方式：0接收合格  1拒收不合格  2全部拒收 3全部接收',
  `version_num` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，处理数据幂等性问题',
  `execution_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '业务执行状态：0未执行  1部分执行  2全部执行',
  `work_order_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联生产工单ID',
  `task_id` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联工序任务ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='报检明细';

-- 5.2 不合格品处理表
CREATE TABLE erp_nonconforming_product (
    id VARCHAR(32) PRIMARY KEY COMMENT '不合格品ID',
    ncp_no VARCHAR(50) NOT NULL COMMENT '不合格品编号',
    inspection_id VARCHAR(32) NOT NULL COMMENT '质检单ID',
    work_order_id VARCHAR(32) NOT NULL COMMENT '生产工单ID',
    defect_type VARCHAR(50) NOT NULL COMMENT '缺陷类型',
    defect_desc TEXT NOT NULL COMMENT '缺陷描述',
    defect_qty DECIMAL(10,3) NOT NULL COMMENT '缺陷数量',
    handling_method TINYINT NOT NULL COMMENT '处理方式：1-返工，2-返修，3-报废，4-让步接收',
    handling_qty DECIMAL(10,3) DEFAULT 0 COMMENT '处理数量',
    handling_cost DECIMAL(10,2) DEFAULT 0 COMMENT '处理成本',
    responsible_dept VARCHAR(100) COMMENT '责任部门',
    responsible_person VARCHAR(50) COMMENT '责任人',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待处理，2-处理中，3-已处理，4-已关闭',
    photos JSON COMMENT '照片附件（JSON格式）',
    remark TEXT COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(32) NOT NULL COMMENT '创建人ID',
    updated_by VARCHAR(32) COMMENT '更新人ID',
    
    UNIQUE KEY uk_ncp_no (ncp_no),
    INDEX idx_inspection_id (inspection_id),
    INDEX idx_work_order_id (work_order_id),
    INDEX idx_handling_method (handling_method),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='不合格品处理表';

-- =====================================================
-- 6. 在制品管理模块
-- =====================================================

在制品管理

**功能描述**：
- 管理生产过程中的在制品库存
- 记录在制品的流转过程
- 支持在制品的批次管理和质量状态管理
- 提供在制品库存的实时查询和统计

**数据结构**：
```sql
CREATE TABLE `erp_wip_inventory` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `material_id` varchar(32) NOT NULL COMMENT '物料ID',
  `norms_id` varchar(32) NOT NULL COMMENT '规格ID',
  `production_order_id` varchar(32) NOT NULL COMMENT '生产工单ID',
  `current_operation_id` varchar(32) NOT NULL COMMENT '当前工序ID',
  `next_operation_id` varchar(32) DEFAULT NULL COMMENT '下一工序ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `status` varchar(20) NOT NULL COMMENT '状态',
  `location` varchar(50) DEFAULT NULL COMMENT '存放位置',
  `batch_no` varchar(50) DEFAULT NULL COMMENT '批次号',
  `production_date` date DEFAULT NULL COMMENT '生产日期',
  `remarks` text COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_production_order_id` (`production_order_id`),
  KEY `idx_current_operation_id` (`current_operation_id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_status` (`status`)
) COMMENT='在制品库存表';

CREATE TABLE `erp_wip_transfer` (
  `id` varchar(32) NOT NULL COMMENT '主键',
  `transfer_code` varchar(50) NOT NULL COMMENT '流转单号',
  `production_order_id` varchar(32) NOT NULL COMMENT '生产工单ID',
  `material_id` varchar(32) NOT NULL COMMENT '物料ID',
  `from_operation_id` varchar(32) NOT NULL COMMENT '来源工序ID',
  `to_operation_id` varchar(32) NOT NULL COMMENT '目标工序ID',
  `from_task_id` varchar(32) DEFAULT NULL COMMENT '来源任务ID',
  `to_task_id` varchar(32) DEFAULT NULL COMMENT '目标任务ID',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `batch_no` varchar(50) DEFAULT NULL COMMENT '批次号',
  `transfer_time` datetime NOT NULL COMMENT '流转时间',
  `transfer_by` varchar(32) NOT NULL COMMENT '流转人',
  `status` varchar(20) NOT NULL COMMENT '状态',
  `remarks` text COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transfer_code` (`transfer_code`),
  KEY `idx_production_order_id` (`production_order_id`),
  KEY `idx_from_operation_id` (`from_operation_id`),
  KEY `idx_to_operation_id` (`to_operation_id`),
  KEY `idx_batch_no` (`batch_no`)
) COMMENT='在制品流转表';
```



-- =====================================================
-- 8. 系统配置模块
-- =====================================================

-- 8.1 系统参数配置表
CREATE TABLE erp_system_config (
    id VARCHAR(32) PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string、number、boolean、json',
    config_group VARCHAR(50) NOT NULL COMMENT '配置分组',
    config_desc VARCHAR(200) COMMENT '配置描述',
    is_system TINYINT NOT NULL DEFAULT 0 COMMENT '是否系统配置：0-否，1-是',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_config_group (config_group)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统参数配置表';

-- =====================================================
-- 9. 初始化数据
-- =====================================================

-- 插入系统配置数据
INSERT INTO erp_system_config (id, config_key, config_value, config_type, config_group, config_desc) VALUES
('cfg001', 'production.auto_start_next_process', 'true', 'boolean', 'production', '工序完工后自动开始下一工序'),
('cfg002', 'production.quality_check_required', 'true', 'boolean', 'production', '是否强制质量检验'),
('cfg003', 'production.work_report_timeout_hours', '24', 'number', 'production', '报工超时提醒时间（小时）'),
('cfg004', 'material.auto_reserve_materials', 'true', 'boolean', 'material', '工单下达时自动预留物料'),
('cfg005', 'outsource.auto_create_purchase', 'false', 'boolean', 'outsource', '委外申请审核后自动创建采购单'),
('cfg006', 'quality.sampling_inspection_rate', '10', 'number', 'quality', '抽检比例（%）'),
('cfg007', 'cost.auto_calculate_cost', 'true', 'boolean', 'cost', '是否自动计算生产成本'),
('cfg008', 'system.operation_log_retention_days', '365', 'number', 'system', '操作日志保留天数');

-- 设置外键约束
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 数据库模型设计说明
-- =====================================================

/*
核心设计特点：

1. 业务完整性：
   - 覆盖生产工单、工序任务、报工、委外、物料、质量、在制品、成本等核心业务
   - 支持工单拆分、并行工序、委外加工等复杂业务场景
   - 完善的状态流转控制和异常处理机制

2. 数据结构优化：
   - 采用VARCHAR(32)作为主键，支持自定义编码规则
   - 合理的字段类型选择，DECIMAL用于精确数值计算
   - JSON字段存储灵活数据（照片、附件等）
   - 完整的索引设计，保证查询性能

3. 扩展性考虑：
   - 预留了大量业务字段，支持后续功能扩展
   - 灵活的配置参数系统，支持业务规则调整
   - 支持多种业务模式（按单生产、库存生产等）

4. 数据完整性：
   - 唯一约束保证关键数据唯一性
   - 状态字段控制业务流程
   - 完整的审计字段（创建人、创建时间、修改人、修改时间）

5. 性能优化：
   - 关键查询字段建立索引
   - 避免过度冗余，保持表结构清晰
   - 支持分区表设计（大数据量场景）

6. 新增功能支持：
   - 工单拆分功能：通过parent_order_id、is_split_parent等字段支持工单拆分业务
   - 双状态管理：state(审核状态)和execution_state(执行状态)分离管理
   - 完整追溯：支持拆分履历、状态变更历史的完整追溯

该数据库模型适用于中小型机械加工企业的生产过程管理，
可以根据具体业务需求进行调整和扩展。

-- =====================================================
-- 字段更新说明
-- =====================================================

/*
erp_machin_header表新增字段说明：

1. 执行状态字段：
   execution_state int(2) NOT NULL DEFAULT '1'
   COMMENT '工单执行状态  1.待下达  2.已下达  3.生产中  4.已完工  5.已入库  6.已关闭'

   用途：按照业务流程设计文档定义的工单执行状态流转
   流转：待下达 → 已下达 → 生产中 → 已完工 → 已入库 → 已关闭

2. 工单拆分相关字段：
   parent_order_id varchar(32) DEFAULT NULL COMMENT '父工单ID（拆分子工单时使用）'
   is_split_parent tinyint(1) DEFAULT '0' COMMENT '是否为拆分父工单：0-否，1-是'
   split_reason varchar(200) DEFAULT NULL COMMENT '拆分原因'
   split_time datetime DEFAULT NULL COMMENT '拆分时间'
   split_by varchar(32) DEFAULT NULL COMMENT '拆分人ID'
   original_quantity decimal(24,6) DEFAULT NULL COMMENT '原始数量（拆分前数量）'

   用途：支持工单拆分业务，建立父子工单关系，记录拆分审计信息

3. 状态字段说明：
   - state: 审核状态（1.新建 2.审核中 3.审核通过 4.审核拒绝 5.已完成 6.完工入库）
   - execution_state: 执行状态（1.待下达 2.已下达 3.生产中 4.已完工 5.已入库 6.已关闭）
   - completion_state: 完工状态（0.未完工 1.已完工）
   - pick_state: 领料状态（1.未领料 2.已领料）

   设计原则：审核流程和执行流程分离，便于业务管理和状态追溯
*/