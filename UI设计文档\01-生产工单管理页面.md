# 生产工单管理页面设计

## 1. 工单创建管理

### 1.1 工单创建页面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ➕ 新建生产工单                                         [保存草稿] [提交审核] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单编号：[自动生成]          工单类型：[生产工单▼]                         │
│ 产品信息：[选择产品▼] 轴承座   规格型号：[φ50×100▼]                        │
│ 计划数量：[100] 件            优先级：[普通▼]                               │
│ 计划开始：[2024-01-15▼]       计划完成：[2024-01-25▼]                      │
│ 生产部门：[机加工车间▼]       负责人：[张主管▼]                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 订单信息                                                                    │
│ 销售订单：[SO240110001▼]      客户名称：[ABC机械公司]                      │
│ 订单数量：[200] 件            交货日期：[2024-01-30]                        │
│ 本次生产：[100] 件            剩余数量：[100] 件                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 技术信息                                                                    │
│ BOM版本：[BOM-轴承座-V2.1▼]   工艺路线：[工艺-轴承座-标准▼]                │
│ 图纸版本：[DWG-2024-001]      技术要求：[按图纸要求执行]                    │
│ 质检标准：[QS-轴承座-001▼]    包装要求：[标准包装]                         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 物料需求预览                                              [刷新物料清单]    │
│ 序号  物料编码    物料名称      规格型号    单位  单耗   总需求   库存状态   │
│ 1     MAT001     45#钢棒      φ55×120    根   1.05   105根    ✅充足      │
│ 2     MAT002     切削液        通用型      升   0.2    20升     ⚠️不足     │
│ 3     MAT003     包装箱        标准型      个   0.1    10个     ✅充足      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序预览                                                  [查看详细工艺]    │
│ 序号  工序名称    工作中心      标准工时   是否质检   是否委外   前置工序   │
│ 10    下料       下料车间      0.5小时    否        否        -          │
│ 20    粗加工     机加工车间    2.0小时    是        否        10         │
│ 30    精加工     机加工车间    1.5小时    是        否        20         │
│ 40    表面处理   表面处理车间  1.0小时    否        是        30         │
│ 50    装配       装配车间      0.8小时    是        否        40         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 备注信息                                                                    │
│ 工单说明：[_________________________________________________]               │
│ 特殊要求：[_________________________________________________]               │
│                                              [取消] [保存草稿] [提交审核]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 工单创建业务逻辑
**创建流程**：
1. **产品选择**：
   - 从产品主数据中选择要生产的产品
   - 自动带出产品的基本信息（规格、单位等）
   - 检查产品是否有有效的BOM和工艺路线

2. **数量和交期设置**：
   - 输入计划生产数量
   - 设置计划开始和完成时间
   - 系统自动校验交期的合理性

3. **技术信息确认**：
   - 选择BOM版本（默认最新有效版本）
   - 选择工艺路线（默认标准工艺）
   - 确认图纸版本和质检标准

4. **物料需求检查**：
   - 根据BOM自动计算物料需求
   - 实时显示库存状态
   - 标识物料短缺情况

5. **工序信息预览**：
   - 根据工艺路线显示工序列表
   - 显示标准工时和质检要求
   - 标识委外工序

6. **保存和提交**：
   - 保存草稿：工单状态为"草稿"
   - 提交审核：工单状态为"待审核"

### 1.3 工单创建API接口

**接口地址：** `POST /api/production/work-orders`

**请求参数：**
```json
{
  "orderNum": "",                    // 工单编号（可选，为空时自动生成）
  "materialId": "MAT001",            // 产品ID
  "normsId": "NORM001",              // 规格ID
  "needNum": 100.000,                // 计划数量
  "priority": 2,                     // 优先级：1-紧急，2-普通，3-低
  "planStartTime": "2024-01-15 08:00:00", // 计划开始时间
  "planEndTime": "2024-01-25 18:00:00",   // 计划完成时间
  "departmentId": "DEPT001",         // 生产部门ID
  "responsibleId": "USER001",        // 负责人ID
  "saleOrderId": "SO240110001",      // 销售订单ID（可选）
  "bomId": "BOM001",                 // BOM版本ID
  "processRouteId": "ROUTE001",      // 工艺路线ID
  "drawingVersion": "DWG-2024-001",  // 图纸版本
  "qualityStandard": "QS-001",       // 质检标准
  "technicalRequirement": "按图纸要求执行", // 技术要求
  "packagingRequirement": "标准包装", // 包装要求
  "orderDesc": "工单说明",           // 工单说明
  "specialRequirement": "特殊要求",   // 特殊要求
  "submitType": "draft"              // 提交类型：draft-保存草稿，submit-提交审核
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单创建成功",
  "data": {
    "id": "WO240115001",
    "orderNum": "MO240115001",
    "state": 1,                      // 1-草稿，2-待审核
    "stateName": "草稿",
    "createdAt": "2024-01-15 10:00:00",
    "materialName": "轴承座",
    "materialSpec": "φ50×100",
    "needNum": 100.000,
    "planStartTime": "2024-01-15 08:00:00",
    "planEndTime": "2024-01-25 18:00:00"
  }
}
```

## 2. 工单状态管理（按业务流程设计文档）

### 1.1 工单执行状态流转
```
待下达 → 已下达 → 生产中 → 已完工 → 已入库 → 已关闭
```

**状态说明**：
- **待下达**：工单已创建并审核通过，等待下达到车间
- **已下达**：工单已下达到车间，自动生成工序任务，可以开始生产
- **生产中**：至少有一个工序已开工
- **已完工**：所有工序都已完工，等待入库
- **已入库**：完工产品已入库
- **已关闭**：工单已关闭，完成整个生产周期

### 1.2 工单列表管理界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🏭 生产工单管理                              [新建工单] [批量下达] [导出]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 执行状态：[全部▼] 审核状态：[全部▼] 部门：[全部▼] 计划完成：[本月▼]       │
│ 工单编号：[_______] 销售订单：[_______]               [搜索] [重置]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单列表                                                                    │
│ ☐ 工单编号    产品名称    计划数量  执行状态    审核状态  领料状态  操作      │
│ ☐ MO240115001 轴承座     100件    🟡待下达    🟢审核通过  未领料    [下达]    │
│ ☐ MO240115002 连接件     200件    🔵生产中    🟢审核通过  已领料    [查看]    │
│ ☐ MO240115003 法兰盘     150件    ✅已完工    🟢审核通过  已领料    [入库]    │
│ ☐ MO240115004 五金件A    300件    🔵生产中    🟢审核通过  已领料    [查看]    │
│ ☐ MO240115005 支架       80件     🟡待下达    🟠审核中    未领料    [查看]    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单统计（按执行状态）                                                      │
│ 总工单：156个  待下达：12个  已下达：8个  生产中：45个  已完工：8个  已入库：83个│
│ 本月新增：28个  本月完工：35个  平均完工周期：5.2天                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. 工单下达管理

### 2.1 工单下达界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 工单下达 - MO240115001                                       [确认下达]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115001  产品名称：轴承座  计划数量：100件                   │
│ 销售订单：SO240110001  部门：机加工车间  计划完成：2024-01-20              │
├─────────────────────────────────────────────────────────────────────────────┤
│ 下达前检查                                                                  │
│ ✅ BOM完整性检查：已通过                                                    │
│ ✅ 物料库存检查：库存充足                                                  │
│ ✅ 工艺路线检查：工艺路线完整                                              │
│ ⚠️  车间产能检查：产能紧张，建议调整计划                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 将自动生成的工序任务                                                        │
│ 工序名称      工作中心    计划工时  标准工时  负责人    备注                │
│ 下料          下料车间    2小时     2小时     张师傅    无                  │
│ 粗加工        机加工车间  8小时     8小时     李师傅    无                  │
│ 精加工        机加工车间  6小时     6小时     王师傅    无                  │
│ 热处理        委外        24小时    24小时    委外      委外工序            │
│ 质检          质检部      1小时     1小时     赵质检    关键质检点          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 下达确认                                                                    │
│ 下达到部门：[机加工车间▼]  下达时间：[2024-01-15 10:00]                   │
│ 备注：[_________________________________________________]                   │
│                                              [取消] [确认下达]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 工单下达业务逻辑
**触发条件**：工单状态为"待下达"（审核通过后）

**下达流程**：
1. **下达前检查**：
   - BOM完整性检查
   - 关键物料库存检查
   - 工艺路线完整性检查
   - 车间产能检查

2. **自动生成工序任务**：
   - 读取工单关联的BOM和工艺路线
   - 根据工序信息自动派工到对应工作中心
   - 生成工序任务单（任务编号：工单号+工序序号）

3. **状态更新**：
   - 工单执行状态：待下达 → 已下达
   - 记录下达时间和下达人

## 3. 工单详情管理

### 3.1 工单详情查看界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 工单详情 - MO240115002                           [编辑] [拆分] [暂停]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单编号：MO240115002  条码：BC240115002  生产计划单：PLAN001              │
│ 产品ID：P001  产品规格ID：SPEC001  计划数量：200件                         │
│ 销售订单：SO240110001  子订单：SUB001  BOM ID：BOM001                      │
│ 部门：机加工车间  创建人：张三  创建时间：2024-01-15 08:00                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 状态信息                                                                    │
│ 执行状态：🔵生产中  审核状态：🟢审核通过  完工状态：进行中  领料状态：已领料│
│ 计划开始：2024-01-15  计划完成：2024-01-20  单据时间：2024-01-15           │
│ 审核人：李四  审核时间：2024-01-15 09:00  审核意见：符合生产要求           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序任务进度                                                                │
│ 工序名称      状态      计划数量  完成数量  开始时间    完成时间    操作      │
│ 下料          已完工    200件    200件    01-15 08:00 01-15 10:00 [查看]    │
│ 粗加工        已完工    200件    200件    01-15 10:30 01-15 18:00 [查看]    │
│ 精加工        生产中    200件    50件     01-16 08:00 -          [报工]    │
│ 热处理        待开工    200件    0件      -          -          [委外]    │
│ 质检          待开工    200件    0件      -          -          [开工]    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 物料领用状态                                                                │
│ 物料名称      规格      计划用量  已领用量  剩余库存  状态      操作        │
│ 钢材A        Q235      100kg    100kg    50kg     已领料    [补料]        │
│ 刀具B        T01       5把      5把      2把      已领料    [补料]        │
│ 切削液        CL-1      10L      10L      15L      已领料    [退料]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 操作记录                                                                    │
│ 时间              操作类型    操作人  说明                                  │
│ 2024-01-15 09:00  审核通过    李四    符合生产要求                          │
│ 2024-01-15 10:00  工单下达    张三    下达到机加工车间                      │
│ 2024-01-15 10:30  工序开工    王五    粗加工工序开始                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. 工单拆分管理

### 4.1 工单拆分界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✂️ 工单拆分 - MO240115001                                       [确认拆分]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 原工单信息                                                                  │
│ 工单编号：MO240115001  产品名称：轴承座  原计划数量：100件                 │
│ 当前状态：🟡待下达  可拆分条件：✅满足                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分方案                                                                    │
│ 拆分原因：[客户要求分批交货▼]                                              │
│ 子工单1：数量[60件] 交期[2024-01-18] 备注[第一批交货]                      │
│ 子工单2：数量[40件] 交期[2024-01-22] 备注[第二批交货]                      │
│                                                          [+添加子工单]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分影响分析                                                                │
│ ✅ 物料分配：可按比例分配                                                  │
│ ✅ 工序任务：将为每个子工单生成独立工序任务                                │
│ ⚠️  交期影响：第二批交期延后2天                                            │
│ ⚠️  成本影响：可能增加5%的管理成本                                         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 拆分确认                                                                    │
│ 拆分后原工单状态将变为"已拆分"，子工单编号：MO240115001-01、MO240115001-02  │
│                                              [取消] [确认拆分]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. 数据库字段映射

### 5.1 erp_machin_header 表字段对应
```
界面元素                    数据库字段                  说明
工单编号                    order_num                  工单唯一标识
条码                        bar_code                   工单条码
产品ID                      material_id                加工的成品id
产品规格ID                  norms_id                   加工的成品规格id
计划数量                    need_num                   需要加工的数量
审核状态                    state                      1.新建 2.审核中 3.审核通过 4.审核拒绝 5.已完成 6.完工入库
执行状态                    execution_state            1.待下达 2.已下达 3.生产中 4.已完工 5.已入库 6.已关闭
完工状态                    completion_state           0.未完工 1.已完工
领料状态                    pick_state                 1.未领料 2.已领料
销售订单                    sales_order_num            关联销售订单号
子订单                      sub_order_num              销售子订单号
BOM ID                      bom_id                     BOM标识
部门                        department_id              生产部门
生产计划单                  production_id              生产计划单号
计划开始时间                start_time                 计划开始时间
计划完成时间                end_time                   计划完成时间
单据时间                    bill_time                  单据创建时间
创建人                      create_id                  创建人ID
创建时间                    create_time                创建时间
审核人                      examine_id                 审核人ID
审核时间                    examine_time               审核时间
审核意见                    examine_content            审核意见
```

### 5.2 工单拆分相关字段
```
父工单ID                    parent_order_id            拆分子工单时的父工单ID
是否拆分父工单              is_split_parent            0-否，1-是
拆分原因                    split_reason               拆分原因说明
拆分时间                    split_time                 拆分操作时间
拆分人                      split_by                   拆分操作人ID
原始数量                    original_quantity          拆分前的原始数量
```

## 6. 状态流转逻辑

### 6.1 工单执行状态流转SQL
```sql
-- 1. 工单下达（待下达 → 已下达）
UPDATE erp_machin_header
SET execution_state = 2,  -- 已下达
    update_time = NOW(),
    update_id = @user_id
WHERE order_num = @order_num
AND execution_state = 1  -- 待下达
AND state = 3;  -- 审核通过

-- 2. 工单开始生产（已下达 → 生产中）
-- 当第一个工序任务开工时自动触发
UPDATE erp_machin_header
SET execution_state = 3  -- 生产中
WHERE id IN (
    SELECT DISTINCT work_order_id
    FROM erp_process_task
    WHERE status = 2  -- 已开工
    AND work_order_id = @work_order_id
)
AND execution_state = 2;  -- 已下达

-- 3. 工单完工（生产中 → 已完工）
-- 当所有工序任务完成时自动触发
UPDATE erp_machin_header
SET execution_state = 4,  -- 已完工
    completion_state = 1  -- 已完工
WHERE id = @work_order_id
AND execution_state = 3  -- 生产中
AND NOT EXISTS (
    SELECT 1 FROM erp_process_task
    WHERE work_order_id = @work_order_id
    AND status NOT IN (4, 6)  -- 非全部完工或已取消
);

-- 4. 工单入库（已完工 → 已入库）
UPDATE erp_machin_header
SET execution_state = 5,  -- 已入库
    state = 6  -- 完工入库（系统状态）
WHERE order_num = @order_num
AND execution_state = 4;  -- 已完工

-- 5. 工单关闭（已入库 → 已关闭）
UPDATE erp_machin_header
SET execution_state = 6  -- 已关闭
WHERE order_num = @order_num
AND execution_state = 5;  -- 已入库
```

### 6.2 工单拆分业务逻辑
```sql
-- 工单拆分主流程
DELIMITER $$
CREATE PROCEDURE SplitWorkOrder(
    IN p_parent_order_id VARCHAR(32),
    IN p_split_reason VARCHAR(200),
    IN p_split_plan JSON,
    IN p_operator_id VARCHAR(32)
)
BEGIN
    DECLARE v_parent_order_num VARCHAR(50);
    DECLARE v_child_count INT DEFAULT 0;
    DECLARE v_child_order_id VARCHAR(32);
    DECLARE v_child_order_num VARCHAR(50);
    DECLARE v_child_quantity DECIMAL(24,6);
    DECLARE v_child_remark VARCHAR(200);
    DECLARE done INT DEFAULT FALSE;

    -- 声明游标处理拆分计划
    DECLARE split_cursor CURSOR FOR
        SELECT quantity, remark FROM JSON_TABLE(
            p_split_plan, '$[*]'
            COLUMNS (
                quantity DECIMAL(24,6) PATH '$.quantity',
                remark VARCHAR(200) PATH '$.remark'
            )
        ) AS jt;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    START TRANSACTION;

    -- 1. 获取父工单信息
    SELECT order_num INTO v_parent_order_num
    FROM erp_machin_header
    WHERE id = p_parent_order_id;

    -- 2. 标记父工单为已拆分
    UPDATE erp_machin_header
    SET is_split_parent = 1,
        split_reason = p_split_reason,
        split_time = NOW(),
        split_by = p_operator_id
    WHERE id = p_parent_order_id;

    -- 3. 创建子工单
    OPEN split_cursor;
    read_loop: LOOP
        FETCH split_cursor INTO v_child_quantity, v_child_remark;
        IF done THEN
            LEAVE read_loop;
        END IF;

        SET v_child_count = v_child_count + 1;
        SET v_child_order_id = CONCAT('WO', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(v_child_count, 3, '0'));
        SET v_child_order_num = CONCAT(v_parent_order_num, '-', LPAD(v_child_count, 2, '0'));

        -- 创建子工单
        INSERT INTO erp_machin_header (
            id, order_num, production_id, department_id, bar_code,
            material_id, norms_id, need_num, start_time, end_time,
            state, execution_state, pick_state, sales_order_num, sub_order_num,
            bom_id, parent_order_id, remark, create_id, create_time
        )
        SELECT
            v_child_order_id, v_child_order_num, production_id, department_id,
            CONCAT('BC', v_child_order_num), material_id, norms_id, v_child_quantity,
            start_time, end_time, 1, 1, 1, sales_order_num, sub_order_num,
            bom_id, p_parent_order_id, v_child_remark, p_operator_id, NOW()
        FROM erp_machin_header
        WHERE id = p_parent_order_id;

    END LOOP;
    CLOSE split_cursor;

    COMMIT;
END$$
DELIMITER ;
```

## 7. 异常状态处理

### 7.1 工单暂停功能
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ⏸️ 工单暂停 - MO240115002                                       [确认暂停]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115002  当前状态：🔵生产中  计划完成：2024-01-20            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 暂停原因                                                                    │
│ 暂停类型：[设备故障▼]                                                      │
│ 暂停原因：[_________________________________________________]               │
│ 预计恢复时间：[2024-01-16 14:00]                                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响分析                                                                    │
│ 🔴 交期影响：预计延期1天                                                   │
│ 🔴 后续工单影响：影响3个后续工单                                           │
│ ⚠️  资源影响：释放设备资源，人员可调配                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处理措施                                                                    │
│ ☐ 通知客户交期变更                                                         │
│ ☐ 调整后续工单计划                                                         │
│ ☐ 安排设备维修                                                             │
│                                              [取消] [确认暂停]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.2 工单取消功能
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ❌ 工单取消 - MO240115001                                       [确认取消]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115001  当前状态：🟡待下达  计划数量：100件                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 取消类型：无条件取消（待下达状态）                                          │
│ 取消原因：[客户取消订单▼]                                                  │
│ 详细说明：[_________________________________________________]               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响分析                                                                    │
│ ✅ 无资源损失：未开始生产，无物料消耗                                      │
│ ✅ 无成本损失：仅设计成本，可回收利用                                      │
│ ⚠️  预留物料：需释放预留的物料                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 处理措施                                                                    │
│ ☐ 释放预留物料                                                             │
│ ☐ 通知相关部门                                                             │
│ ☐ 更新生产计划                                                             │
│                                              [取消] [确认取消]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.3 工单关闭页面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔒 工单关闭 - MO240115003                                       [确认关闭]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115003     产品名称：法兰盘     计划数量：150件              │
│ 当前状态：已入库          完工数量：148件      合格数量：145件              │
│ 计划完成：2024-01-20      实际完成：2024-01-19  提前：1天                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 成本核算确认                                                                │
│ 材料成本：￥12,500.00     人工成本：￥3,200.00   制造费用：￥1,800.00       │
│ 委外费用：￥2,100.00      质量损失：￥150.00     总成本：￥19,750.00        │
│ 单位成本：￥133.11/件     预算成本：￥18,900.00  成本差异：+￥850.00        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 完工确认                                                                    │
│ 入库数量：[145] 件        入库时间：[2024-01-19 16:30]                     │
│ 报废数量：[3] 件          报废原因：[质量不合格]                            │
│ 剩余数量：[2] 件          处理方式：[返工▼]                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ 关闭确认                                                                    │
│ 关闭原因：[正常完工▼]     关闭说明：[工单正常完工，成本核算完成]            │
│ 财务确认：☑ 已确认成本核算  质量确认：☑ 已确认质量结果                     │
│ 仓库确认：☑ 已确认入库完成  计划确认：☑ 已更新生产计划                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 关闭后处理                                                                  │
│ ☐ 释放剩余物料预留                                                         │
│ ☐ 归档技术文档                                                             │
│ ☐ 更新设备使用记录                                                         │
│ ☐ 生成完工报告                                                             │
│                                              [取消] [确认关闭]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.4 工单关闭API接口

**接口地址：** `POST /api/production/work-orders/{id}/close`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "closeReason": "normal_completion",    // 关闭原因：normal_completion-正常完工，force_close-强制关闭
  "closeDesc": "工单正常完工，成本核算完成", // 关闭说明
  "actualCompletedQty": 145.000,         // 实际完工数量
  "qualifiedQty": 145.000,               // 合格数量
  "scrapQty": 3.000,                     // 报废数量
  "scrapReason": "质量不合格",           // 报废原因
  "remainingQty": 2.000,                 // 剩余数量
  "remainingHandling": "rework",         // 剩余处理方式：rework-返工，scrap-报废，inventory-入库
  "actualCost": 19750.00,                // 实际总成本
  "materialCost": 12500.00,              // 材料成本
  "laborCost": 3200.00,                  // 人工成本
  "manufacturingCost": 1800.00,          // 制造费用
  "outsourceCost": 2100.00,              // 委外费用
  "qualityLoss": 150.00,                 // 质量损失
  "financeConfirmed": true,              // 财务确认
  "qualityConfirmed": true,              // 质量确认
  "warehouseConfirmed": true,            // 仓库确认
  "planningConfirmed": true,             // 计划确认
  "postActions": [                       // 关闭后处理
    "release_material_reservation",
    "archive_documents",
    "update_equipment_records",
    "generate_completion_report"
  ]
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单关闭成功",
  "data": {
    "id": "WO240115003",
    "orderNum": "MO240115003",
    "executionState": 6,               // 已关闭
    "executionStateName": "已关闭",
    "closeTime": "2024-01-20 10:00:00",
    "actualCompletedQty": 145.000,
    "actualCost": 19750.00,
    "costVariance": 850.00,
    "completionRate": 96.67
  }
}
```

### 7.5 工单恢复页面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ▶️ 工单恢复 - MO240115004                                       [确认恢复]  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工单信息                                                                    │
│ 工单编号：MO240115004     产品名称：齿轮       计划数量：200件              │
│ 当前状态：已暂停          暂停时间：2024-01-18 14:30                        │
│ 暂停原因：设备故障        暂停天数：2天                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 恢复条件检查                                                                │
│ 设备状态：✅ 设备已修复    物料状态：✅ 物料充足                            │
│ 人员状态：✅ 人员到位      工艺状态：✅ 工艺正常                            │
│ 质量状态：✅ 质检设备正常  安全状态：✅ 安全条件满足                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 恢复计划                                                                    │
│ 恢复时间：[2024-01-20 08:00▼]  预计完成：[2024-01-28 18:00▼]              │
│ 调整原因：[设备故障已修复，恢复正常生产]                                    │
│ 新的优先级：[紧急▼]        负责人：[李主管▼]                               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 影响分析                                                                    │
│ 延期天数：2天             影响订单：SO240110005                            │
│ 成本影响：+￥500.00       客户影响：需要通知客户延期                        │
│ 后续工单：MO240115005(受影响)  建议措施：加班生产追回进度                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 恢复确认                                                                    │
│ ☐ 已通知相关人员                                                           │
│ ☐ 已更新生产计划                                                           │
│ ☐ 已通知客户延期                                                           │
│ ☐ 已安排加班计划                                                           │
│                                              [取消] [确认恢复]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.6 工单恢复API接口

**接口地址：** `POST /api/production/work-orders/{id}/resume`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "resumeTime": "2024-01-20 08:00:00",  // 恢复时间
  "newPlanEndTime": "2024-01-28 18:00:00", // 新的计划完成时间
  "adjustReason": "设备故障已修复，恢复正常生产", // 调整原因
  "newPriority": 1,                      // 新的优先级：1-紧急，2-普通，3-低
  "newResponsibleId": "USER002",         // 新负责人ID
  "impactAnalysis": {                    // 影响分析
    "delayDays": 2,                      // 延期天数
    "affectedOrders": ["SO240110005"],   // 影响的订单
    "costImpact": 500.00,                // 成本影响
    "customerImpact": "需要通知客户延期", // 客户影响
    "followUpOrders": ["MO240115005"],   // 后续受影响工单
    "suggestedActions": "加班生产追回进度" // 建议措施
  },
  "confirmations": [                     // 恢复确认项
    "notify_personnel",
    "update_production_plan",
    "notify_customer",
    "arrange_overtime"
  ]
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单恢复成功",
  "data": {
    "id": "WO240115004",
    "orderNum": "MO240115004",
    "executionState": 3,               // 生产中
    "executionStateName": "生产中",
    "resumeTime": "2024-01-20 08:00:00",
    "newPlanEndTime": "2024-01-28 18:00:00",
    "pauseDuration": 2,                // 暂停天数
    "delayDays": 2
  }
}
```

### 7.7 批量操作页面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📦 批量操作管理                                         [批量下达] [批量暂停] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 操作类型：[批量下达▼]     目标状态：[已下达]     选中数量：5个工单          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 选中工单列表                                            [全选] [反选] [清空] │
│ ☑ MO240115001  轴承座    100件  待下达  2024-01-15  机加工车间             │
│ ☑ MO240115002  法兰盘    150件  待下达  2024-01-16  机加工车间             │
│ ☑ MO240115003  齿轮      200件  待下达  2024-01-17  机加工车间             │
│ ☑ MO240115004  联轴器    80件   待下达  2024-01-18  装配车间               │
│ ☑ MO240115005  支架      120件  待下达  2024-01-19  焊接车间               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 批量下达设置                                                                │
│ 下达时间：[2024-01-20 08:00▼]  下达人：[当前用户]                         │
│ 统一优先级：[普通▼]           是否检查物料：☑                              │
│ 统一负责人：[张主管▼]         是否检查产能：☑                              │
├─────────────────────────────────────────────────────────────────────────────┤
│ 预检查结果                                                                  │
│ ✅ MO240115001：物料充足，产能充足，可以下达                               │
│ ✅ MO240115002：物料充足，产能充足，可以下达                               │
│ ⚠️  MO240115003：物料不足(缺少钢材5根)，建议延后下达                       │
│ ✅ MO240115004：物料充足，产能充足，可以下达                               │
│ ✅ MO240115005：物料充足，产能充足，可以下达                               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 操作确认                                                                    │
│ 成功下达：4个工单         失败：1个工单(MO240115003)                       │
│ 备注说明：[批量下达生产工单，MO240115003因物料不足暂缓下达]                │
│                                              [取消] [确认执行]             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.8 批量操作API接口

**接口地址：** `POST /api/production/work-orders/batch-operation`

**请求参数：**
```json
{
  "operation": "batch_issue",           // 操作类型：batch_issue-批量下达，batch_pause-批量暂停，batch_cancel-批量取消
  "workOrderIds": [                     // 工单ID列表
    "WO240115001",
    "WO240115002",
    "WO240115003",
    "WO240115004",
    "WO240115005"
  ],
  "operationTime": "2024-01-20 08:00:00", // 操作时间
  "operatorId": "USER001",              // 操作人ID
  "settings": {                         // 批量操作设置
    "unifiedPriority": 2,               // 统一优先级（可选）
    "unifiedResponsibleId": "USER002",  // 统一负责人（可选）
    "checkMaterial": true,              // 是否检查物料
    "checkCapacity": true,              // 是否检查产能
    "pauseReason": "设备维护",          // 暂停原因（批量暂停时）
    "cancelReason": "计划变更"          // 取消原因（批量取消时）
  },
  "remark": "批量下达生产工单，MO240115003因物料不足暂缓下达" // 备注说明
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "批量操作完成",
  "data": {
    "operation": "batch_issue",
    "totalCount": 5,                    // 总数量
    "successCount": 4,                  // 成功数量
    "failureCount": 1,                  // 失败数量
    "operationTime": "2024-01-20 08:00:00",
    "results": [                        // 详细结果
      {
        "workOrderId": "WO240115001",
        "orderNum": "MO240115001",
        "success": true,
        "message": "下达成功"
      },
      {
        "workOrderId": "WO240115002",
        "orderNum": "MO240115002",
        "success": true,
        "message": "下达成功"
      },
      {
        "workOrderId": "WO240115003",
        "orderNum": "MO240115003",
        "success": false,
        "message": "物料不足，缺少钢材5根"
      },
      {
        "workOrderId": "WO240115004",
        "orderNum": "MO240115004",
        "success": true,
        "message": "下达成功"
      },
      {
        "workOrderId": "WO240115005",
        "orderNum": "MO240115005",
        "success": true,
        "message": "下达成功"
      }
    ]
  }
}
```

## 8. 业务规则总结

### 8.1 工单下达规则
1. **前置条件**：工单必须处于"审核通过"状态
2. **检查项目**：BOM完整性、物料库存、工艺路线、车间产能
3. **自动操作**：生成工序任务、更新执行状态、记录操作日志

### 8.2 工单拆分规则
1. **拆分条件**：待下达、已下达（未开工）、生产中（特殊处理）
2. **拆分权限**：生产计划员、生产经理
3. **最小数量**：不能小于工艺要求的最小批量
4. **物料完整性**：每个拆分工单都要有完整的物料配套

### 8.3 状态流转规则
1. **自动流转**：工序开工触发"生产中"，全部完工触发"已完工"
2. **手动流转**：工单下达、入库确认、工单关闭
3. **异常处理**：支持暂停、取消、恢复等异常状态处理
4. **权限控制**：不同状态的操作需要相应权限

## 9. API接口设计

### 9.1 工单列表查询接口

**接口地址：** `GET /api/production/work-orders`

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 20,
  "orderNum": "",                    // 工单编号（模糊查询）
  "materialId": "",                  // 产品ID
  "normsId": "",                     // 规格ID
  "state": null,                     // 审核状态：1-新建，2-审核中，3-审核通过，4-审核拒绝，5-已完成，6-完工入库
  "executionState": null,            // 执行状态：1-待下达，2-已下达，3-生产中，4-已完工，5-已入库，6-已关闭
  "completionState": null,           // 完工状态：0-未完工，1-已完工
  "pickState": null,                 // 领料状态：1-未领料，2-已领料
  "departmentId": "",                // 部门ID
  "startTimeBegin": "",              // 计划开始时间-开始
  "startTimeEnd": "",                // 计划开始时间-结束
  "endTimeBegin": "",                // 计划完成时间-开始
  "endTimeEnd": "",                  // 计划完成时间-结束
  "createTimeBegin": "",             // 创建时间-开始
  "createTimeEnd": "",               // 创建时间-结束
  "salesOrderNum": "",               // 销售订单号
  "isSplitParent": null              // 是否拆分父工单：0-否，1-是
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": "WO240115001",
        "orderNum": "MO240115001",
        "productionId": "PLAN001",
        "departmentId": "DEPT001",
        "barCode": "/uploads/barcode/MO240115001.png",
        "materialId": "MAT001",
        "normsId": "NORM001",
        "needNum": 100.000000,
        "startTime": "2024-01-15 08:00:00",
        "endTime": "2024-01-20 18:00:00",
        "state": 3,
        "executionState": 2,
        "pickState": 1,
        "examineId": "USER001",
        "examineContent": "审核通过",
        "examineTime": "2024-01-15 09:00:00",
        "enclosureInfo": "",
        "remark": "紧急订单",
        "createId": "USER002",
        "createTime": "2024-01-15 08:00:00",
        "salesOrderNum": "SO240115001",
        "subOrderNum": "SUB001",
        "bomId": "BOM001",
        "operTime": "2024-01-15 08:00:00",
        "completionState": 0,
        "processInstanceId": "PROC001",
        "submitType": 2,
        "versionNum": 1,
        "parentOrderId": null,
        "isSplitParent": 0,
        "splitReason": null,
        "splitTime": null,
        "splitBy": null,
        "originalQuantity": null,
        // 关联信息
        "materialName": "轴承座",
        "materialCode": "MAT001",
        "normsName": "Φ50×100",
        "departmentName": "机加工车间",
        "createUserName": "张三",
        "examineUserName": "李四"
      }
    ]
  }
}
```

### 9.2 工单详情查询接口

**接口地址：** `GET /api/production/work-orders/{id}`

**路径参数：**
- `id`: 工单ID

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "WO240115001",
    "orderNum": "MO240115001",
    "productionId": "PLAN001",
    "departmentId": "DEPT001",
    "barCode": "/uploads/barcode/MO240115001.png",
    "materialId": "MAT001",
    "normsId": "NORM001",
    "needNum": 100.000000,
    "startTime": "2024-01-15 08:00:00",
    "endTime": "2024-01-20 18:00:00",
    "state": 3,
    "executionState": 2,
    "pickState": 1,
    "examineId": "USER001",
    "examineContent": "审核通过",
    "examineTime": "2024-01-15 09:00:00",
    "enclosureInfo": "",
    "remark": "紧急订单",
    "createId": "USER002",
    "createTime": "2024-01-15 08:00:00",
    "salesOrderNum": "SO240115001",
    "subOrderNum": "SUB001",
    "bomId": "BOM001",
    "operTime": "2024-01-15 08:00:00",
    "completionState": 0,
    "processInstanceId": "PROC001",
    "submitType": 2,
    "versionNum": 1,
    "parentOrderId": null,
    "isSplitParent": 0,
    "splitReason": null,
    "splitTime": null,
    "splitBy": null,
    "originalQuantity": null,
    // 关联信息
    "materialName": "轴承座",
    "materialCode": "MAT001",
    "normsName": "Φ50×100",
    "departmentName": "机加工车间",
    "createUserName": "张三",
    "examineUserName": "李四",
    // 工序任务信息
    "processTasks": [
      {
        "id": "TASK001",
        "taskNo": "T240115001",
        "processId": "OP001",
        "processCode": "OP001",
        "processName": "下料",
        "processSeq": 1,
        "planQuantity": 100.000000,
        "status": 2,
        "isQualityCheck": 0
      }
    ]
  }
}
```

### 9.3 工单下达接口

**接口地址：** `POST /api/production/work-orders/{id}/issue`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "remark": "正常下达"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单下达成功",
  "data": {
    "id": "WO240115001",
    "executionState": 2,
    "updateTime": "2024-01-15 10:00:00"
  }
}
```

### 9.4 工单拆分接口

**接口地址：** `POST /api/production/work-orders/{id}/split`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "splitReason": "交期要求分批交付",
  "splitPlan": [
    {
      "quantity": 60.000000,
      "remark": "第一批次"
    },
    {
      "quantity": 40.000000,
      "remark": "第二批次"
    }
  ]
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单拆分成功",
  "data": {
    "parentOrderId": "WO240115001",
    "childOrders": [
      {
        "id": "WO240115001-01",
        "orderNum": "MO240115001-01",
        "needNum": 60.000000
      },
      {
        "id": "WO240115001-02",
        "orderNum": "MO240115001-02",
        "needNum": 40.000000
      }
    ]
  }
}
```

### 9.5 工单状态更新接口

**接口地址：** `PUT /api/production/work-orders/{id}/status`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "executionState": 5,               // 目标执行状态
  "remark": "产品已入库"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": {
    "id": "WO240115001",
    "executionState": 5,
    "updateTime": "2024-01-20 16:00:00"
  }
}
```

### 9.6 工单暂停接口

**接口地址：** `POST /api/production/work-orders/{id}/pause`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "pauseType": "设备故障",
  "pauseReason": "主轴设备故障，需要维修",
  "estimatedResumeTime": "2024-01-16 14:00:00"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单暂停成功",
  "data": {
    "id": "WO240115001",
    "pauseTime": "2024-01-15 15:30:00",
    "estimatedResumeTime": "2024-01-16 14:00:00"
  }
}
```

### 9.7 工单取消接口

**接口地址：** `POST /api/production/work-orders/{id}/cancel`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "cancelReason": "客户取消订单",
  "cancelDescription": "客户临时取消订单，无需继续生产"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单取消成功",
  "data": {
    "id": "WO240115001",
    "executionState": 7,               // 已取消状态
    "cancelTime": "2024-01-15 16:00:00"
  }
}
```

### 9.8 工单统计接口

**接口地址：** `GET /api/production/work-orders/statistics`

**请求参数：**
```json
{
  "dateType": "week",                // 统计周期：day/week/month/year
  "startDate": "2024-01-15",
  "endDate": "2024-01-21",
  "departmentId": ""                 // 部门ID（可选）
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "summary": {
      "totalOrders": 156,
      "completedOrders": 89,
      "inProgressOrders": 45,
      "pendingOrders": 22,
      "completionRate": 57.1
    },
    "statusDistribution": [
      {"status": 1, "statusName": "待下达", "count": 22},
      {"status": 2, "statusName": "已下达", "count": 18},
      {"status": 3, "statusName": "生产中", "count": 27},
      {"status": 4, "statusName": "已完工", "count": 34},
      {"status": 5, "statusName": "已入库", "count": 55}
    ],
    "dailyTrend": [
      {"date": "2024-01-15", "completed": 12, "started": 8},
      {"date": "2024-01-16", "completed": 15, "started": 10},
      {"date": "2024-01-17", "completed": 18, "started": 12}
    ]
  }
}
```

### 9.9 工单导出接口

**接口地址：** `POST /api/production/work-orders/export`

**请求参数：**
```json
{
  "orderNums": ["MO240115001", "MO240115002"],  // 工单编号列表（可选）
  "exportType": "excel",                        // 导出类型：excel/pdf
  "includeDetails": true,                       // 是否包含详细信息
  "filters": {                                  // 筛选条件（与列表查询相同）
    "state": 3,
    "executionState": 2
  }
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "downloadUrl": "/api/files/download/work-orders-20240115.xlsx",
    "fileName": "生产工单列表-20240115.xlsx",
    "fileSize": 1024000
  }
}
```

### 9.10 工单创建接口

**接口地址：** `POST /api/production/work-orders`

**请求参数：**
```json
{
  "orderNum": "",                    // 工单编号（可选，为空时自动生成）
  "materialId": "MAT001",            // 产品ID
  "normsId": "NORM001",              // 规格ID
  "needNum": 100.000,                // 计划数量
  "priority": 2,                     // 优先级：1-紧急，2-普通，3-低
  "planStartTime": "2024-01-15 08:00:00", // 计划开始时间
  "planEndTime": "2024-01-25 18:00:00",   // 计划完成时间
  "departmentId": "DEPT001",         // 生产部门ID
  "responsibleId": "USER001",        // 负责人ID
  "saleOrderId": "SO240110001",      // 销售订单ID（可选）
  "bomId": "BOM001",                 // BOM版本ID
  "processRouteId": "ROUTE001",      // 工艺路线ID
  "drawingVersion": "DWG-2024-001",  // 图纸版本
  "qualityStandard": "QS-001",       // 质检标准
  "technicalRequirement": "按图纸要求执行", // 技术要求
  "packagingRequirement": "标准包装", // 包装要求
  "orderDesc": "工单说明",           // 工单说明
  "specialRequirement": "特殊要求",   // 特殊要求
  "submitType": "draft"              // 提交类型：draft-保存草稿，submit-提交审核
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单创建成功",
  "data": {
    "id": "WO240115001",
    "orderNum": "MO240115001",
    "state": 1,                      // 1-草稿，2-待审核
    "stateName": "草稿",
    "createdAt": "2024-01-15 10:00:00",
    "materialName": "轴承座",
    "materialSpec": "φ50×100",
    "needNum": 100.000,
    "planStartTime": "2024-01-15 08:00:00",
    "planEndTime": "2024-01-25 18:00:00"
  }
}
```

### 9.11 工单关闭接口

**接口地址：** `POST /api/production/work-orders/{id}/close`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "closeReason": "normal_completion",    // 关闭原因：normal_completion-正常完工，force_close-强制关闭
  "closeDesc": "工单正常完工，成本核算完成", // 关闭说明
  "actualCompletedQty": 145.000,         // 实际完工数量
  "qualifiedQty": 145.000,               // 合格数量
  "scrapQty": 3.000,                     // 报废数量
  "scrapReason": "质量不合格",           // 报废原因
  "remainingQty": 2.000,                 // 剩余数量
  "remainingHandling": "rework",         // 剩余处理方式：rework-返工，scrap-报废，inventory-入库
  "actualCost": 19750.00,                // 实际总成本
  "materialCost": 12500.00,              // 材料成本
  "laborCost": 3200.00,                  // 人工成本
  "manufacturingCost": 1800.00,          // 制造费用
  "outsourceCost": 2100.00,              // 委外费用
  "qualityLoss": 150.00,                 // 质量损失
  "financeConfirmed": true,              // 财务确认
  "qualityConfirmed": true,              // 质量确认
  "warehouseConfirmed": true,            // 仓库确认
  "planningConfirmed": true,             // 计划确认
  "postActions": [                       // 关闭后处理
    "release_material_reservation",
    "archive_documents",
    "update_equipment_records",
    "generate_completion_report"
  ]
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单关闭成功",
  "data": {
    "id": "WO240115003",
    "orderNum": "MO240115003",
    "executionState": 6,               // 已关闭
    "executionStateName": "已关闭",
    "closeTime": "2024-01-20 10:00:00",
    "actualCompletedQty": 145.000,
    "actualCost": 19750.00,
    "costVariance": 850.00,
    "completionRate": 96.67
  }
}
```

### 9.12 工单恢复接口

**接口地址：** `POST /api/production/work-orders/{id}/resume`

**路径参数：**
- `id`: 工单ID

**请求参数：**
```json
{
  "resumeTime": "2024-01-20 08:00:00",  // 恢复时间
  "newPlanEndTime": "2024-01-28 18:00:00", // 新的计划完成时间
  "adjustReason": "设备故障已修复，恢复正常生产", // 调整原因
  "newPriority": 1,                      // 新的优先级：1-紧急，2-普通，3-低
  "newResponsibleId": "USER002",         // 新负责人ID
  "impactAnalysis": {                    // 影响分析
    "delayDays": 2,                      // 延期天数
    "affectedOrders": ["SO240110005"],   // 影响的订单
    "costImpact": 500.00,                // 成本影响
    "customerImpact": "需要通知客户延期", // 客户影响
    "followUpOrders": ["MO240115005"],   // 后续受影响工单
    "suggestedActions": "加班生产追回进度" // 建议措施
  },
  "confirmations": [                     // 恢复确认项
    "notify_personnel",
    "update_production_plan",
    "notify_customer",
    "arrange_overtime"
  ]
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "工单恢复成功",
  "data": {
    "id": "WO240115004",
    "orderNum": "MO240115004",
    "executionState": 3,               // 生产中
    "executionStateName": "生产中",
    "resumeTime": "2024-01-20 08:00:00",
    "newPlanEndTime": "2024-01-28 18:00:00",
    "pauseDuration": 2,                // 暂停天数
    "delayDays": 2
  }
}
```

### 9.13 批量操作接口

**接口地址：** `POST /api/production/work-orders/batch-operation`

**请求参数：**
```json
{
  "operation": "batch_issue",           // 操作类型：batch_issue-批量下达，batch_pause-批量暂停，batch_cancel-批量取消
  "workOrderIds": [                     // 工单ID列表
    "WO240115001",
    "WO240115002",
    "WO240115003",
    "WO240115004",
    "WO240115005"
  ],
  "operationTime": "2024-01-20 08:00:00", // 操作时间
  "operatorId": "USER001",              // 操作人ID
  "settings": {                         // 批量操作设置
    "unifiedPriority": 2,               // 统一优先级（可选）
    "unifiedResponsibleId": "USER002",  // 统一负责人（可选）
    "checkMaterial": true,              // 是否检查物料
    "checkCapacity": true,              // 是否检查产能
    "pauseReason": "设备维护",          // 暂停原因（批量暂停时）
    "cancelReason": "计划变更"          // 取消原因（批量取消时）
  },
  "remark": "批量下达生产工单，MO240115003因物料不足暂缓下达" // 备注说明
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "批量操作完成",
  "data": {
    "operation": "batch_issue",
    "totalCount": 5,                    // 总数量
    "successCount": 4,                  // 成功数量
    "failureCount": 1,                  // 失败数量
    "operationTime": "2024-01-20 08:00:00",
    "results": [                        // 详细结果
      {
        "workOrderId": "WO240115001",
        "orderNum": "MO240115001",
        "success": true,
        "message": "下达成功"
      },
      {
        "workOrderId": "WO240115002",
        "orderNum": "MO240115002",
        "success": true,
        "message": "下达成功"
      },
      {
        "workOrderId": "WO240115003",
        "orderNum": "MO240115003",
        "success": false,
        "message": "物料不足，缺少钢材5根"
      },
      {
        "workOrderId": "WO240115004",
        "orderNum": "MO240115004",
        "success": true,
        "message": "下达成功"
      },
      {
        "workOrderId": "WO240115005",
        "orderNum": "MO240115005",
        "success": true,
        "message": "下达成功"
      }
    ]
  }
}
```
