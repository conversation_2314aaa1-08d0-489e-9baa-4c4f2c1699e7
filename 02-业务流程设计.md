# 生产过程管理详细业务流程设计

## 1. 生产工单状态管理

### 1.1 工单状态定义

待下达 → 已下达 → 生产中 → 已完工 → 已入库 → 已关闭

**状态说明**：
- **待下达**：工单已创建，但尚未开始生产
- **已下达**：工单已下达到车间，可以开始生产
- **生产中**：至少有一个工序已开工
- **已完工**：所有工序都已完工，等待入库
- **已入库**：产品已完工入库
- **已关闭**：工单完全结束，包括成本核算等

### 1.2 状态触发场景

**待下达 → 已下达**：
- 触发条件：手工下达或定时下达
- 触发操作：生产计划员点击"下达工单"
- 系统动作：
  - 检查BOM和工艺路线完整性
  - 检查关键物料库存是否充足
  - 自动生成所有工序任务
  - 工单状态更新为"已下达"

**已下达 → 生产中**：
- 触发条件：任意工序开工
- 触发操作：操作工开始第一个工序
- 系统动作：
  - 工单状态自动更新为"生产中"
  - 记录生产开始时间
  - 开始跟踪生产进度

**生产中 → 已完工**：
- 触发条件：所有工序都已完工
- 触发操作：最后一个工序完工确认
- 系统动作：
  - 检查所有工序状态
  - 统计最终完工数量
  - 工单状态更新为"已完工"
  - 触发完工入库流程

**已完工 → 已入库**：
- 触发条件：完工产品入库完成
- 触发操作：仓库确认入库
- 系统动作：
  - 更新库存数量
  - 工单状态更新为"已入库"
  - 生成完工库单

**已入库 → 已关闭**：
- 触发条件：成本核算完成或手工关闭
- 触发操作：财务确认成本或管理员关闭
- 系统动作：
  - 锁定工单数据
  - 完成成本核算
  - 工单状态更新为"已关闭"

### 1.3 异常状态处理

**暂停状态**：
- 适用于：生产中的工单
- 触发场景：设备故障、物料短缺、质量问题等
- 恢复条件：异常问题解决后可恢复
- 操作权限：车间主任、生产经理

**取消状态**：
- 适用状态：待下达、已下达、生产中（有条件）
- 操作权限：生产经理以上级别

**取消场景分类**：

1. **无条件取消**（待下达状态）：
   - 触发场景：客户取消订单、设计变更、计划调整
   - 前置条件：工单状态为"待下达"
   - 处理方式：直接取消，无资源损失

2. **简单取消**（已下达状态）：
   - 触发场景：客户紧急取消、重大设计变更
   - 前置条件：
     - 工单状态为"已下达"
     - 所有工序状态为"待开工"
     - 未发生生产活动
   - 处理方式：直接取消，释放预留物料

3. **复杂取消**（生产中状态）：
   - 触发场景：客户取消订单、产品停产、质量问题无法解决
   - 前置条件：
     - 工单状态为"生产中"
     - 需要处理已消耗的物料和工时
   - 处理方式：需要成本核算和损失处理

**强制取消机制**：
- 权限要求：总经理或系统管理员
- 适用场景：紧急情况、系统错误、特殊业务需求
- 处理方式：
  - 记录强制取消原因
  - 生成损失清单（物料、工时、设备占用）
  - 触发成本核算流程
  - 处理在制品和半成品

**取消后处理流程**：

1. **物料处理**：
   - 已领用未消耗：办理退库
   - 已消耗物料：计入损失成本
   - 在制品/半成品：评估后处理（入库、报废、转其他用途）

2. **工时处理**：
   - 已发生工时：计入实际成本
   - 设备占用时间：记录设备利用率影响

3. **委外处理**：
   - 已发出委外：联系供应商停止加工或收回
   - 委外费用：按实际发生计算

4. **财务处理**：
   - 生成取消成本报告
   - 更新成本核算
   - 如涉及客户责任，生成索赔清单

**取消操作界面设计**：
```
取消确认界面：
- 取消原因：[下拉选择] + [文本说明]
- 影响评估：
  * 已消耗物料：XXX元
  * 已发生工时：XXX小时
  * 在制品数量：XXX件
  * 委外费用：XXX元
- 处理方案：
  * 在制品处理：[入库/报废/转用途]
  * 委外处理：[继续/停止/收回]
- 审批流程：[提交审批/强制执行]
```

### 1.4 工单拆分业务

**拆分场景**：
1. **产能限制**：单个工单数量过大，需要分批生产
2. **交期要求**：客户要求分批交货
3. **物料短缺**：部分物料到货，先生产部分数量
4. **设备安排**：不同设备并行生产，提高效率
5. **质量风险**：大批量生产风险高，分批降低风险

**拆分条件**：
- 工单状态：待下达、已下达（未开工）、生产中（特殊处理）
- 拆分权限：生产计划员、生产经理（生产中拆分需要更高权限）
- 最小拆分数量：不能小于工艺要求的最小批量
- 物料完整性：每个拆分工单都要有完整的物料配套

**生产中拆分特殊处理**：

1. **拆分前置条件**：
   - 当前工序必须是完整批次（不能在工序执行中途拆分）
   - 已完成工序的在制品数量必须明确
   - 后续工序尚未开始或可以重新安排
   - 需要生产经理或更高级别权限审批

2. **拆分时点选择**：
   ```
   可拆分时点：
   - 工序完工点：某道工序全部完成后
   - 质检完成点：质检合格后进行拆分
   - 转序等待点：等待下道工序开始前
   
   不可拆分时点：
   - 工序执行中：设备正在加工
   - 质检进行中：产品在检验状态
   - 异常处理中：存在质量问题待处理
   ```

3. **在制品处理**：
    
    **方案A：按完工工序基数拆分（推荐）**：
    ```
    拆分原则：
    - 以最后一个完全完工的工序数量作为拆分基数
    - 后续工序的在制品暂不分配，等待处理完成
    - 确保拆分数据的准确性和可操作性
    
    示例：
    原工单：1000件
    工序1已完工：800件
    工序2进行中：600件（400件完成，200件进行中）
    工序3未开始：0件
    
    拆分基数：800件（工序1完工数量）
    拆分方案：子工单A（300件），子工单B（500件）
    
    分配结果：
    - 子工单A：工序1完成300件，工序2-3待重新安排
    - 子工单B：工序1完成500件，工序2-3待重新安排
    - 剩余处理：工序2的600件在制品完成后，按比例补充到各子工单
    ```
    
    **方案B：精确在制品拆分（复杂场景）**：
    ```
    适用场景：
    - 紧急订单，不能等待工序完成
    - 客户明确要求特定数量的在制品
    - 设备故障需要紧急转移生产
    
    拆分原则：
    - 已完成工序：按拆分比例分配
    - 当前工序在制品：按实际完成数量精确分配
    - 需要详细的在制品盘点和确认
    
    示例：
    原工单：1000件，工序1完工800件，工序2进行中600件
    紧急拆分：子工单A需要400件（含在制品）
    
    分配结果：
    - 子工单A：工序1完成320件，工序2完成240件，总计560件在制品
    - 子工单B：工序1完成480件，工序2完成360件，剩余数量
    ```
    
    **拆分方案选择建议**：
    - 常规情况：优先选择方案A，确保数据准确性
    - 紧急情况：可选择方案B，但需要详细的影响评估
    - 系统默认：方案A，需要方案B时需要特殊审批

4. **成本重新分摊**：
   ```sql
   -- 生产中拆分成本处理
   UPDATE erp_production_cost 
   SET allocated_cost = original_cost * (split_quantity / original_quantity)
   WHERE order_id = @parent_order_id 
   AND process_completed = 1;
   
   -- 为子工单创建成本记录
   INSERT INTO erp_production_cost (
     order_id, process_id, material_cost, 
     labor_cost, overhead_cost, allocated_cost
   ) SELECT 
     @child_order_id, process_id, 
     material_cost * @split_ratio,
     labor_cost * @split_ratio,
     overhead_cost * @split_ratio,
     allocated_cost * @split_ratio
   FROM erp_production_cost 
   WHERE order_id = @parent_order_id;
   ```

5. **工序状态继承**：
   ```
   工序状态分配规则：
   - 已完成工序：子工单继承"已完成"状态
   - 进行中工序：
     * 按实际完成数量分配到各子工单
     * 未完成部分重新计算工序参数
   - 未开始工序：重新计算并保持"未开始"状态
   
   工序参数调整：
   - 加工时间：按数量比例重新计算
   - 设备占用：重新安排设备计划
   - 人员安排：根据新的批量调整人员需求
   ```
7. **质量记录继承**：
   - 已完成的质检记录按比例分配到各子工单
   - 在检产品需要重新标识归属
   - 质量问题和不合格品需要明确归属
   - 后续质检按新的批次进行

8. **生产计划调整**：
   ```
   计划重排原则：
   - 保持原有交期承诺（除非客户同意调整）
   - 优化设备和人员安排
   - 考虑物料到货计划
   - 平衡各子工单的生产节奏
   
   排程调整：
   - 重新计算各子工单的工序计划时间
   - 调整设备占用计划
   - 更新人员工作安排
   - 同步更新MRP计划
   ```

9. **拆分操作界面**：
   ```
   生产中拆分界面：
   - 当前生产状态展示：
     * 各工序完成情况
     * 在制品数量统计
     * 质量检验状态
   
   - 拆分方案设计：
     * 拆分时点选择
     * 在制品分配方案
     * 成本分摊预览
     * 计划调整影响
   
   - 风险提示：
     * 拆分对交期的影响
     * 成本核算的复杂性
     * 质量追溯的影响
     * 需要的额外资源
   
   - 审批流程：
     * 拆分申请理由
     * 影响评估报告
     * 审批意见记录
   ```

10. **拆分后跟踪**：
    - 建立详细的拆分履历
    - 跟踪各子工单的执行情况
    - 对比拆分前后的效率变化
    - 记录拆分决策的效果评估

**拆分业务流程**：

1. **拆分申请**：
   ```
   原工单信息：
   - 工单号：MO202401001
   - 产品：五金件A
   - 数量：1000件
   - 交期：2024-02-15
   
   拆分方案：
   - 拆分数量：3个子工单
   - 子工单1：300件，交期：2024-02-05
   - 子工单2：400件，交期：2024-02-10  
   - 子工单3：300件，交期：2024-02-15
   ```

2. **拆分校验**：
   - 检查拆分数量总和 = 原工单数量
   - 检查每个子工单数量 ≥ 最小批量
   - 检查物料库存是否支持拆分
   - 检查工艺路线完整性

3. **拆分执行**：
   - 原工单状态变更为"已拆分"
   - 生成子工单（编号规则：原工单号-01、-02、-03）
   - 分配BOM和工艺路线
   - 分配物料需求
   - 继承原工单的基本信息

**拆分后数据处理**：

1. **工单关系管理**：
   ```sql
   -- 工单拆分关系表
   CREATE TABLE erp_production_order_split (
     id varchar(32) PRIMARY KEY,
     parent_order_id varchar(32) NOT NULL COMMENT '父工单ID',
     child_order_id varchar(32) NOT NULL COMMENT '子工单ID', 
     split_sequence int NOT NULL COMMENT '拆分序号',
     split_quantity decimal(10,2) NOT NULL COMMENT '拆分数量',
     split_reason varchar(200) COMMENT '拆分原因',
     split_time datetime NOT NULL COMMENT '拆分时间',
     split_by varchar(32) NOT NULL COMMENT '拆分人'
   );
   ```

2. **物料需求分配**：
   - 按拆分比例分配原材料需求
   - 重新计算各子工单的物料清单
   - 更新物料预留关系

3. **工艺路线继承**：
   - 每个子工单继承完整的工艺路线
   - 工序参数按比例调整（如批量相关参数）
   - 保持工序间的依赖关系

**拆分规则设计**：

1. **数量拆分规则**：
   - 支持按数量拆分：指定每个子工单的具体数量
   - 支持按比例拆分：按百分比自动计算数量
   - 支持平均拆分：平均分配到指定数量的子工单

2. **交期拆分规则**：
   - 子工单交期不能晚于原工单交期
   - 支持递进交期：第一批最早，依次递延
   - 考虑工艺周期：确保生产时间充足

3. **优先级继承**：
   - 子工单继承原工单的优先级
   - 支持调整子工单间的相对优先级
   - 紧急子工单可以提高优先级

**拆分后管理**：

1. **进度跟踪**：
   - 分别跟踪每个子工单的生产进度
   - 汇总显示原工单的整体进度
   - 支持子工单间的进度对比

2. **成本核算**：
   - 分别核算每个子工单的成本
   - 汇总计算原工单的总成本
   - 支持成本差异分析

3. **质量管理**：
   - 每个子工单独立进行质量检验
   - 质量问题不影响其他子工单
   - 支持批次质量追溯

**拆分撤销**：
- 适用条件：所有子工单都未开工
- 撤销流程：
  1. 检查子工单状态
  2. 释放子工单占用的资源
  3. 恢复原工单状态为"待下达"
  4. 删除拆分记录

**界面设计要点**：
```
拆分界面：
- 原工单信息展示
- 拆分方案设计：
  * 拆分方式：[按数量/按比例/平均拆分]
  * 子工单数量：[输入框]
  * 数量分配：[表格编辑]
  * 交期分配：[日期选择]
- 影响分析：
  * 物料需求变化
  * 产能占用情况
  * 交期可行性分析
- 确认拆分：[预览/执行]
```

## 2. 总体业务流程

### 2.1 主流程图
```
生产工单下达 → 工序任务自动生成 → 工序执行/委外处理 → 质量检验 → 
部分完工 → 流转下道工序（并行） → 全部完工 → 完工入库
```

### 2.2 流程分支
- **内部工序**：工序执行 → 质检 → 完工
- **委外工序**：委外申请 → 委外发料 → 委外跟踪 → 委外收货 → 质检 → 完工
- **质检不合格**：不合格处理 → 返工/报废/让步接收

## 3. 工序任务管理

### 3.1 工序任务自动生成
**触发条件**：生产工单状态变更为"已下达"

**生成逻辑**：
1. 读取工单关联的工程方案
2. 获取工艺流程中的所有工序
3. 根据工序信息自动派工到对应工作中心
4. 生成工序任务单

**工序任务单内容**：
```
- 任务编号：自动生成（工单号+工序序号）
- 工单信息：工单号、产品信息、数量
- 工序信息：工序名称、工序代码、工序说明
- 派工信息：工作中心、预计工时、优先级
- 物料信息：所需原材料清单
- 质检要求：是否需要质检、质检标准
- 委外标识：是否委外工序、委外供应商
- 前置工序：依赖的前道工序
```

### 3.2 工序任务状态管理
**状态流转**：
```
待开工 → 已开工 → 部分完工 → 全部完工 → 已质检 → 已转序/已入库
```

**并行执行机制**：
- **部分完工**：当前工序完成部分数量，可以开始下道工序
- **并行生产**：下道工序可以在上道工序未全部完工时开始
- **批次管理**：按批次记录每批工件的流转情况

**特殊状态**：
- **委外中**：委外工序专用状态
- **质检不合格**：质检失败状态
- **返工中**：不合格品返工状态

## 4. 内部工序执行流程

### 4.0 生产物料管理设计

#### 4.0.1 物料管理策略

**当前实施：按单领料**
- 工单下达时统一领料
- 物料挂载到工单级别
- 操作简单，便于管理

**预留扩展：按工序领料**
- 数据结构支持工序级关联
- 可根据需要灵活切换
- 支持混合模式（部分按单，部分按工序）

#### 4.0.2 可扩展的数据结构设计

**生产领料单（支持多种模式）**：
```sql
-- 生产领料单主表
CREATE TABLE erp_material_requisition (
  id varchar(32) PRIMARY KEY,
  requisition_code varchar(50) NOT NULL COMMENT '领料单号',
  requisition_type varchar(20) NOT NULL COMMENT '领料类型：按单领料/按工序领料/补料',
  production_order_id varchar(32) NOT NULL COMMENT '生产工单ID',
  operation_task_id varchar(32) NULL COMMENT '工序任务ID（按工序领料时必填）',
  requisition_date datetime NOT NULL COMMENT '领料日期',
  requisition_by varchar(32) NOT NULL COMMENT '领料人',
  status varchar(20) DEFAULT '待发料' COMMENT '状态：待发料/已发料/已关闭',
  remarks varchar(200) COMMENT '备注'
);

-- 生产领料单明细表
CREATE TABLE erp_material_requisition_detail (
  id varchar(32) PRIMARY KEY,
  requisition_id varchar(32) NOT NULL COMMENT '领料单ID',
  material_id varchar(32) NOT NULL COMMENT '物料ID',
  required_quantity decimal(10,3) NOT NULL COMMENT '需求数量',
  issued_quantity decimal(10,3) DEFAULT 0 COMMENT '已发数量',
  target_operation_id varchar(32) NULL COMMENT '目标工序ID（预留字段）'
);
```



**统一物料单据表设计**：
```sql
-- 生产物料单据主表（统一管理领料、退料、补料）
CREATE TABLE erp_material_document (
  id varchar(32) PRIMARY KEY,
  document_code varchar(50) NOT NULL COMMENT '单据号',
  document_type varchar(20) NOT NULL COMMENT '单据类型：领料/退料/补料',
  sub_type varchar(20) COMMENT '子类型：按单领料/按工序领料/余料退库/质量退料/异常退料',
  production_order_id varchar(32) NOT NULL COMMENT '生产工单ID',
  operation_task_id varchar(32) NULL COMMENT '工序任务ID（按工序领料时必填）',
  related_document_id varchar(32) NULL COMMENT '关联单据ID（退料时关联原领料单）',
  document_date datetime NOT NULL COMMENT '单据日期',
  operator_id varchar(32) NOT NULL COMMENT '操作人',
  status varchar(20) DEFAULT '待处理' COMMENT '状态：待处理/已处理/已关闭',
  reason varchar(200) COMMENT '原因说明（退料、补料时必填）',
  remarks varchar(200) COMMENT '备注',
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 生产物料单据明细表
CREATE TABLE erp_material_document_detail (
  id varchar(32) PRIMARY KEY,
  document_id varchar(32) NOT NULL COMMENT '单据ID',
  material_id varchar(32) NOT NULL COMMENT '物料ID',
  planned_quantity decimal(10,3) COMMENT '计划数量（领料时使用）',
  actual_quantity decimal(10,3) NOT NULL COMMENT '实际数量',
  target_operation_id varchar(32) NULL COMMENT '目标工序ID（预留字段）',
  reason varchar(200) COMMENT '明细原因说明'
);
```

**单据类型说明**：
```
document_type 主类型：
- '领料'：从仓库领取物料到生产现场
- '退料'：从生产现场退回物料到仓库  
- '补料'：补充不足的物料

sub_type 子类型：
领料类：按单领料、按工序领料
退料类：余料退库、质量退料、异常退料
补料类：不足补料、质量补料、工艺补料
```

**系统配置表（控制领料模式）**：
```sql
-- 物料管理配置表
CREATE TABLE erp_material_config (
  id varchar(32) PRIMARY KEY,
  config_type varchar(50) NOT NULL COMMENT '配置类型',
  config_key varchar(100) NOT NULL COMMENT '配置键',
  config_value varchar(200) NOT NULL COMMENT '配置值',
  description varchar(200) COMMENT '说明'
);

-- 示例配置数据
INSERT INTO erp_material_config VALUES 
('material_requisition', 'default_mode', 'by_order', '默认领料模式：by_order按单/by_operation按工序'),
('material_requisition', 'allow_mixed_mode', 'true', '是否允许混合模式'),
('material_requisition', 'auto_generate', 'true', '是否自动生成领料单');
```

#### 4.0.3 当前实施的操作流程

**按单领料流程**：
```
1. 工单下达时自动生成整单领料单（排除委外工序物料）
2. 仓库按单统一发料
3. 物料发放到工单对应的现场区域
4. 各工序从现场区域取用物料
```

**委外工序物料特殊处理**：
```sql
-- 生成工单领料单时排除委外工序物料
SELECT 
  bom.material_id,
  m.material_code,
  m.material_name,
  SUM(bom.required_quantity * @order_quantity) as total_required_qty
FROM erp_operation_bom bom
LEFT JOIN erp_material m ON bom.material_id = m.id
LEFT JOIN erp_operation op ON bom.operation_id = op.id
WHERE bom.production_order_id = @order_id
  AND op.is_outsourced = false  -- 排除委外工序
GROUP BY bom.material_id, m.material_code, m.material_name;
```

**委外工序物料标识**：
```sql
-- 标识委外工序物料，留在仓库
SELECT 
  bom.material_id,
  m.material_code,
  m.material_name,
  bom.required_quantity,
  '委外工序专用，留在仓库' as remark
FROM erp_operation_bom bom
LEFT JOIN erp_material m ON bom.material_id = m.id
LEFT JOIN erp_operation op ON bom.operation_id = op.id
WHERE bom.production_order_id = @order_id
  AND op.is_outsourced = true;  -- 委外工序物料
```



**补料流程**：
```
1. 操作工在报工界面申请补料
2. 系统生成补料单（requisition_type='补料'）
3. 班组长审核
4. 仓库发料
```

#### 4.0.4 当前操作界面

**工单物料管理界面**：
```
工单物料状态
┌─────────────────────────────────────────────────┐
│ 工单：MO240101  状态：生产中                    │
├─────────────────────────────────────────────────┤
│ 已领物料清单：                                  │
│ 原材料A  计划:100kg  已领:100kg  已用:30kg      │
│ 刀具B    计划:10把   已领:10把   已用:3把       │
│ 切削液   计划:20L    已领:20L    已用:8L        │
├─────────────────────────────────────────────────┤
│ 委外工序物料（留在仓库）：                      │
│ 热处理剂  计划:5kg   库存:15kg   状态:待委外    │
│ 保护气体  计划:2L    库存:10L    状态:待委外    │
├─────────────────────────────────────────────────┤
│ [申请补料] [余料退库] [消耗明细] [异常处理]     │
└─────────────────────────────────────────────────┘
```

**委外工序物料管理界面**：
```
委外工序物料管理
┌─────────────────────────────────────────────────┐
│ 工单：MO240101  委外工序：热处理                │
├─────────────────────────────────────────────────┤
│ 委外物料清单：                                  │
│ 热处理剂  需要:5kg   库存:15kg   状态:待发料    │
│ 保护气体  需要:2L    库存:10L    状态:待发料    │
├─────────────────────────────────────────────────┤
│ 委外厂家：XX热处理厂                            │
│ 发料地址：XX市XX区XX路XX号                      │
├─────────────────────────────────────────────────┤
│ [确认发料] [修改数量] [更换厂家]                │
└─────────────────────────────────────────────────┘
```

**委外发料确认界面**：
```
委外发料确认
┌─────────────────────────────────────────────────┐
│ 工单：MO240101  委外工序：热处理                │
├─────────────────────────────────────────────────┤
│ 在制品发料：                                    │
│ 五金件A   数量:100件  从:精加工车间  到:XX热处理厂 │
├─────────────────────────────────────────────────┤
│ 原材料/辅料发料：                               │
│ 热处理剂  数量:5kg   从:原材料仓库  到:XX热处理厂  │
│ 保护气体  数量:2L    从:辅料仓库    到:XX热处理厂  │
├─────────────────────────────────────────────────┤
│ 发料时间：2024-01-15 14:00                      │
│ 发料人员：张三                                  │
│ 备注：[输入框]                                  │
├─────────────────────────────────────────────────┤
│                [确认发料]  [取消]               │
└─────────────────────────────────────────────────┘
```

**工序报工界面**：
```
工序报工
┌─────────────────────────────────────────────────┐
│ 工序：粗加工 (MO240101-010)  操作工：张三       │
├─────────────────────────────────────────────────┤
│ 报工信息：                                      │
│ 完成数量：[50] 件  合格数量：[48] 件            │
├─────────────────────────────────────────────────┤
│ [提交报工] [申请补料] [异常报告]                │
└─────────────────────────────────────────────────┘
```

### 4.2 详细报工操作流程设计

#### 4.2.1 报工触发方式

**1. 主动报工**：
- 操作工在工序完成后主动进行报工
- 支持部分报工和完工报工
- 可以多次报工直到工序全部完成

**2. 定时提醒报工**：
- 系统根据标准工时自动提醒报工
- 超时未报工自动发送提醒消息
- 班组长可查看未报工任务列表

**3. 扫码报工**：
- 扫描工序任务二维码快速进入报工界面
- 扫描产品条码自动填充部分信息
- 支持批量扫码报工

**4. 移动端报工**：
- 支持手机、平板等移动设备报工
- 现场拍照记录加工过程
- 语音输入备注信息

#### 4.2.2 极简报工操作流程

**设计理念**：
- **极简操作**：工人只需填写2个核心数据：完成数量、合格数量
- **智能补全**：系统自动计算工时、效率、不合格数量等
- **默认正常**：90%的报工都是正常情况，默认无异常
- **秒级完成**：正常报工15秒内完成，异常报工才需要详细填写

**极简报工界面**：
```
📱 工序报工（手机界面）
┌─────────────────────────┐
│ 🏭 粗加工 MO240101-010  │
│ 👤 张三  📊 30/100件    │
├─────────────────────────┤
│ 本次完成：[50] 件       │
│ 合格数量：[50] 件       │
├─────────────────────────┤
│ ⏰ 4.0小时 (自动计算)   │
│ 📈 效率 105% (自动)     │
├─────────────────────────┤
│ 🔧 □ 有异常需要记录     │
├─────────────────────────┤
│ [🚀 提交] [💾 草稿]     │
└─────────────────────────┘
```

**三种报工模式**：

**1. 快速报工（90%场景）**：
```
操作步骤：
1. 输入完成数量：50
2. 输入合格数量：50（默认等于完成数量）
3. 点击提交
总用时：15秒

系统自动处理：
- 不合格数量 = 完成数量 - 合格数量 = 0
- 工时 = 当前时间 - 开工时间 = 4.0小时
- 效率 = 标准工时 / 实际工时 * 100% = 105%
- 状态 = 正常生产（默认）
```

**2. 异常报工（8%场景）**：
```
操作步骤：
1. 输入完成数量：45
2. 输入合格数量：40
3. 勾选"有异常需要记录"
4. 选择异常类型：质量问题
5. 填写不合格原因：材料硬度不够
6. 点击提交
总用时：1-2分钟

系统记录：
- 不合格数量 = 45 - 40 = 5件
- 异常类型 = 质量问题
- 异常描述 = 材料硬度不够
- 自动通知质检员和班组长
```

**3. 停机报工（2%场景）**：
```
操作步骤：
1. 输入完成数量：20
2. 输入合格数量：20
3. 勾选"有异常需要记录"
4. 选择异常类型：设备故障
5. 填写停机时间：120分钟
6. 填写故障描述：主轴轴承异响
7. 点击提交
总用时：2-3分钟

系统处理：
- 有效工时 = 总工时 - 停机时间
- 效率计算基于有效工时
- 自动通知设备维修和班组长
```

**智能化功能**：
```sql
-- 极简报工数据处理
CREATE PROCEDURE ProcessSimpleWorkReport(
    @task_id VARCHAR(32),
    @completed_qty DECIMAL(10,2),
    @qualified_qty DECIMAL(10,2),
    @has_exception BOOLEAN DEFAULT FALSE,
    @exception_detail JSON DEFAULT NULL
)
BEGIN
    DECLARE @start_time DATETIME;
    DECLARE @actual_hours DECIMAL(8,2);
    DECLARE @defective_qty DECIMAL(10,2);
    DECLARE @efficiency_rate DECIMAL(5,2);
    
    -- 获取开工时间
    SELECT start_time INTO @start_time
    FROM erp_operation_task WHERE id = @task_id;
    
    -- 自动计算数据
    SET @actual_hours = TIMESTAMPDIFF(MINUTE, @start_time, NOW()) / 60.0;
    SET @defective_qty = @completed_qty - @qualified_qty;
    
    -- 处理停机时间
    IF @has_exception AND JSON_EXTRACT(@exception_detail, '$.downtime_minutes') IS NOT NULL THEN
        SET @actual_hours = @actual_hours - (JSON_EXTRACT(@exception_detail, '$.downtime_minutes') / 60.0);
    END IF;
    
    -- 计算效率
    SELECT (@completed_qty / planned_quantity) / (@actual_hours / standard_hours) * 100
    INTO @efficiency_rate
    FROM erp_operation_task t
    JOIN erp_operation_standard s ON t.operation_id = s.operation_id
    WHERE t.id = @task_id;
    
    -- 插入报工记录
    INSERT INTO erp_work_report_simple (
        id, task_id, operator_id, report_time,
        completed_quantity, qualified_quantity, defective_quantity,
        actual_hours, efficiency_rate, has_exception, exception_detail
    ) VALUES (
        UUID(), @task_id, 
        (SELECT operator_id FROM erp_operation_task WHERE id = @task_id),
        NOW(), @completed_qty, @qualified_qty, @defective_qty,
        @actual_hours, @efficiency_rate, @has_exception, @exception_detail
    );
    
    -- 异常处理
    IF @has_exception THEN
        CALL HandleWorkException(@task_id, @exception_detail);
    END IF;
    
    -- 更新任务状态
    UPDATE erp_operation_task 
    SET completed_quantity = completed_quantity + @completed_qty,
        qualified_quantity = qualified_quantity + @qualified_qty,
        last_report_time = NOW()
    WHERE id = @task_id;
END;
```

#### 4.2.3 报工数据处理

**1. 数据验证**：
```sql
-- 报工数据完整性验证
CREATE PROCEDURE ValidateWorkReport(
    @task_id VARCHAR(32),
    @completed_qty DECIMAL(10,2),
    @qualified_qty DECIMAL(10,2),
    @defective_qty DECIMAL(10,2),
    @actual_hours DECIMAL(8,2)
)
BEGIN
    DECLARE @planned_qty DECIMAL(10,2);
    DECLARE @previous_completed DECIMAL(10,2);
    DECLARE @standard_hours DECIMAL(8,2);
    
    -- 获取计划数量和已完成数量
    SELECT planned_quantity, 
           COALESCE(SUM(completed_quantity), 0)
    INTO @planned_qty, @previous_completed
    FROM erp_operation_task t
    LEFT JOIN erp_work_report r ON t.id = r.task_id
    WHERE t.id = @task_id;
    
    -- 验证数量
    IF @completed_qty > (@planned_qty - @previous_completed) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '完成数量超过剩余数量';
    END IF;
    
    IF (@qualified_qty + @defective_qty) != @completed_qty THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '合格数量与不合格数量之和不等于完成数量';
    END IF;
    
    -- 验证工时合理性
    SELECT standard_hours INTO @standard_hours
    FROM erp_operation_standard 
    WHERE operation_id = (SELECT operation_id FROM erp_operation_task WHERE id = @task_id);
    
    IF @actual_hours > (@standard_hours * 2) THEN
        INSERT INTO erp_work_report_alert (task_id, alert_type, alert_message)
        VALUES (@task_id, '工时异常', '实际工时超过标准工时200%');
    END IF;
END;
```

**2. 数据存储**：
```sql
-- 报工主表
CREATE TABLE erp_work_report (
    id VARCHAR(32) PRIMARY KEY,
    report_code VARCHAR(50) NOT NULL COMMENT '报工单号',
    task_id VARCHAR(32) NOT NULL COMMENT '工序任务ID',
    operator_id VARCHAR(32) NOT NULL COMMENT '操作工ID',
    report_time DATETIME NOT NULL COMMENT '报工时间',
    completed_quantity DECIMAL(10,2) NOT NULL COMMENT '完成数量',
    qualified_quantity DECIMAL(10,2) NOT NULL COMMENT '合格数量',
    defective_quantity DECIMAL(10,2) NOT NULL COMMENT '不合格数量',
    actual_hours DECIMAL(8,2) NOT NULL COMMENT '实际工时',
    direct_hours DECIMAL(8,2) COMMENT '直接加工工时',
    setup_hours DECIMAL(8,2) COMMENT '调试工时',
    inspection_hours DECIMAL(8,2) COMMENT '检查工时',
    downtime_hours DECIMAL(8,2) COMMENT '停机工时',
    time_efficiency DECIMAL(5,2) COMMENT '工时效率%',
    quantity_efficiency DECIMAL(5,2) COMMENT '数量效率%',
    overall_efficiency DECIMAL(5,2) COMMENT '综合效率%',
    status VARCHAR(20) DEFAULT '已提交' COMMENT '状态',
    remarks TEXT COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 不合格品明细表
CREATE TABLE erp_defective_detail (
    id VARCHAR(32) PRIMARY KEY,
    report_id VARCHAR(32) NOT NULL COMMENT '报工单ID',
    defect_type VARCHAR(50) NOT NULL COMMENT '缺陷类型',
    defect_quantity DECIMAL(10,2) NOT NULL COMMENT '缺陷数量',
    defect_reason VARCHAR(200) COMMENT '缺陷原因',
    handling_method VARCHAR(100) COMMENT '处理方式',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**3. 状态更新**：
```sql
-- 更新工序任务状态
UPDATE erp_operation_task 
SET completed_quantity = completed_quantity + @completed_qty,
    qualified_quantity = qualified_quantity + @qualified_qty,
    defective_quantity = defective_quantity + @defective_qty,
    actual_hours = actual_hours + @actual_hours,
    last_report_time = NOW(),
    status = CASE 
        WHEN (completed_quantity + @completed_qty) >= planned_quantity THEN '已完工'
        ELSE '生产中'
    END
WHERE id = @task_id;

-- 更新在制品库存
UPDATE erp_wip_inventory 
SET quantity = quantity + @qualified_qty
WHERE production_order_id = @order_id 
  AND current_operation_id = @operation_id;
```

### 4.3 工时记录和管理机制

#### 4.3.1 工时记录体系

**1. 工时分类定义**：
```sql
-- 工时分类字典
CREATE TABLE erp_work_hour_type (
    id VARCHAR(32) PRIMARY KEY,
    type_code VARCHAR(20) NOT NULL COMMENT '工时类型编码',
    type_name VARCHAR(50) NOT NULL COMMENT '工时类型名称',
    is_productive BOOLEAN DEFAULT TRUE COMMENT '是否为生产性工时',
    cost_factor DECIMAL(3,2) DEFAULT 1.00 COMMENT '成本系数',
    description TEXT COMMENT '描述'
);

-- 初始化工时类型
INSERT INTO erp_work_hour_type VALUES
('WH001', 'DIRECT', '直接加工工时', TRUE, 1.00, '直接用于产品加工的工时'),
('WH002', 'SETUP', '设备调试工时', TRUE, 1.00, '设备调试、换模具等准备工时'),
('WH003', 'INSPECT', '质量检查工时', TRUE, 1.00, '首件检查、过程检查等质量工时'),
('WH004', 'MAINTAIN', '设备维护工时', FALSE, 0.80, '设备保养、维修等工时'),
('WH005', 'WAIT_MATERIAL', '等待物料工时', FALSE, 0.00, '因物料短缺等待的工时'),
('WH006', 'WAIT_INSTRUCTION', '等待指令工时', FALSE, 0.00, '等待生产指令的工时'),
('WH007', 'BREAKDOWN', '设备故障工时', FALSE, 0.00, '设备故障停机工时'),
('WH008', 'REWORK', '返工工时', TRUE, 1.20, '不合格品返工的工时');
```

**2. 工时记录表设计**：
```sql
-- 详细工时记录表
CREATE TABLE erp_work_hour_detail (
    id VARCHAR(32) PRIMARY KEY,
    report_id VARCHAR(32) NOT NULL COMMENT '报工单ID',
    task_id VARCHAR(32) NOT NULL COMMENT '工序任务ID',
    operator_id VARCHAR(32) NOT NULL COMMENT '操作工ID',
    hour_type_code VARCHAR(20) NOT NULL COMMENT '工时类型',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    duration_hours DECIMAL(8,2) NOT NULL COMMENT '持续时间(小时)',
    quantity_produced DECIMAL(10,2) COMMENT '对应产量',
    efficiency_rate DECIMAL(5,2) COMMENT '效率%',
    cost_amount DECIMAL(10,2) COMMENT '工时成本',
    remarks TEXT COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 工时汇总表
CREATE TABLE erp_work_hour_summary (
    id VARCHAR(32) PRIMARY KEY,
    task_id VARCHAR(32) NOT NULL COMMENT '工序任务ID',
    operator_id VARCHAR(32) NOT NULL COMMENT '操作工ID',
    work_date DATE NOT NULL COMMENT '工作日期',
    total_hours DECIMAL(8,2) NOT NULL COMMENT '总工时',
    productive_hours DECIMAL(8,2) NOT NULL COMMENT '生产性工时',
    non_productive_hours DECIMAL(8,2) NOT NULL COMMENT '非生产性工时',
    direct_hours DECIMAL(8,2) COMMENT '直接工时',
    indirect_hours DECIMAL(8,2) COMMENT '间接工时',
    overtime_hours DECIMAL(8,2) COMMENT '加班工时',
    efficiency_rate DECIMAL(5,2) COMMENT '综合效率%',
    total_cost DECIMAL(10,2) COMMENT '总成本',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_task_operator_date (task_id, operator_id, work_date)
);
```

#### 4.3.2 工时自动记录机制

**1. 开工自动记录**：
```sql
-- 开工时自动创建工时记录
CREATE TRIGGER tr_start_work_hour
AFTER UPDATE ON erp_operation_task
FOR EACH ROW
BEGIN
    IF NEW.status = '已开工' AND OLD.status != '已开工' THEN
        INSERT INTO erp_work_hour_detail (
            id, task_id, operator_id, hour_type_code,
            start_time, end_time, duration_hours
        ) VALUES (
            UUID(), NEW.id, NEW.operator_id, 'DIRECT',
            NEW.actual_start_time, NEW.actual_start_time, 0
        );
    END IF;
END;
```

**2. 实时工时更新**：
```sql
-- 定时更新进行中任务的工时
CREATE EVENT evt_update_current_work_hours
ON SCHEDULE EVERY 15 MINUTE
DO
BEGIN
    UPDATE erp_work_hour_detail d
    JOIN erp_operation_task t ON d.task_id = t.id
    SET d.end_time = NOW(),
        d.duration_hours = TIMESTAMPDIFF(MINUTE, d.start_time, NOW()) / 60.0
    WHERE t.status = '生产中' 
      AND d.hour_type_code = 'DIRECT'
      AND d.end_time = d.start_time;
END;
```

**3. 停机时间记录**：
```sql
-- 停机记录表
CREATE TABLE erp_downtime_record (
    id VARCHAR(32) PRIMARY KEY,
    task_id VARCHAR(32) NOT NULL COMMENT '工序任务ID',
    downtime_type VARCHAR(50) NOT NULL COMMENT '停机类型',
    start_time DATETIME NOT NULL COMMENT '停机开始时间',
    end_time DATETIME COMMENT '停机结束时间',
    duration_minutes INT COMMENT '停机时长(分钟)',
    reason_code VARCHAR(20) COMMENT '停机原因代码',
    reason_description TEXT COMMENT '停机原因描述',
    impact_quantity DECIMAL(10,2) COMMENT '影响产量',
    responsible_dept VARCHAR(50) COMMENT '责任部门',
    status VARCHAR(20) DEFAULT '记录中' COMMENT '状态',
    created_by VARCHAR(32) NOT NULL COMMENT '记录人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 停机原因字典
CREATE TABLE erp_downtime_reason (
    reason_code VARCHAR(20) PRIMARY KEY,
    reason_name VARCHAR(100) NOT NULL COMMENT '原因名称',
    category VARCHAR(50) NOT NULL COMMENT '原因分类',
    responsible_dept VARCHAR(50) COMMENT '责任部门',
    is_planned BOOLEAN DEFAULT FALSE COMMENT '是否计划停机',
    cost_impact DECIMAL(3,2) DEFAULT 1.00 COMMENT '成本影响系数'
);
```

#### 4.3.3 工时效率分析

**1. 实时效率计算**：
```sql
-- 工时效率计算函数
DELIMITER //
CREATE FUNCTION CalculateWorkEfficiency(
    task_id VARCHAR(32),
    actual_hours DECIMAL(8,2),
    completed_qty DECIMAL(10,2)
) RETURNS DECIMAL(5,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE standard_hours DECIMAL(8,2);
    DECLARE planned_qty DECIMAL(10,2);
    DECLARE time_efficiency DECIMAL(5,2);
    DECLARE quantity_efficiency DECIMAL(5,2);
    DECLARE overall_efficiency DECIMAL(5,2);
    
    -- 获取标准工时和计划数量
    SELECT os.standard_hours, ot.planned_quantity
    INTO standard_hours, planned_qty
    FROM erp_operation_task ot
    JOIN erp_operation_standard os ON ot.operation_id = os.operation_id
    WHERE ot.id = task_id;
    
    -- 计算时间效率
    SET time_efficiency = (standard_hours / actual_hours) * 100;
    
    -- 计算数量效率
    SET quantity_efficiency = (completed_qty / planned_qty) * 100;
    
    -- 计算综合效率
    SET overall_efficiency = (time_efficiency + quantity_efficiency) / 2;
    
    RETURN overall_efficiency;
END //
DELIMITER ;
```

**2. 效率分析报表**：
```sql
-- 操作工效率分析
SELECT 
    o.operator_name,
    COUNT(DISTINCT t.id) as task_count,
    SUM(r.actual_hours) as total_hours,
    SUM(r.completed_quantity) as total_quantity,
    AVG(r.time_efficiency) as avg_time_efficiency,
    AVG(r.quantity_efficiency) as avg_quantity_efficiency,
    AVG(r.overall_efficiency) as avg_overall_efficiency,
    SUM(r.actual_hours * oh.hourly_rate) as total_cost
FROM erp_work_report r
JOIN erp_operation_task t ON r.task_id = t.id
JOIN erp_operator o ON r.operator_id = o.id
JOIN erp_operator_hourly_rate oh ON o.id = oh.operator_id
WHERE r.report_time >= '2024-01-01'
  AND r.report_time < '2024-02-01'
GROUP BY o.id, o.operator_name
ORDER BY avg_overall_efficiency DESC;

-- 工序效率分析
SELECT 
    op.operation_name,
    COUNT(r.id) as report_count,
    AVG(r.time_efficiency) as avg_time_efficiency,
    AVG(r.quantity_efficiency) as avg_quantity_efficiency,
    SUM(r.actual_hours) as total_hours,
    SUM(r.completed_quantity) as total_quantity,
    SUM(r.actual_hours) / SUM(r.completed_quantity) as hours_per_unit
FROM erp_work_report r
JOIN erp_operation_task t ON r.task_id = t.id
JOIN erp_operation op ON t.operation_id = op.id
WHERE r.report_time >= '2024-01-01'
GROUP BY op.id, op.operation_name
ORDER BY avg_time_efficiency DESC;
```

#### 4.3.4 工时成本核算

**1. 工时成本计算**：
```sql
-- 操作工小时费率表
CREATE TABLE erp_operator_hourly_rate (
    id VARCHAR(32) PRIMARY KEY,
    operator_id VARCHAR(32) NOT NULL COMMENT '操作工ID',
    skill_level VARCHAR(20) NOT NULL COMMENT '技能等级',
    base_hourly_rate DECIMAL(8,2) NOT NULL COMMENT '基础小时费率',
    overtime_rate DECIMAL(8,2) NOT NULL COMMENT '加班费率',
    night_shift_rate DECIMAL(8,2) COMMENT '夜班费率',
    weekend_rate DECIMAL(8,2) COMMENT '周末费率',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 工时成本计算
UPDATE erp_work_hour_detail d
JOIN erp_operator_hourly_rate r ON d.operator_id = r.operator_id
SET d.cost_amount = d.duration_hours * 
    CASE 
        WHEN HOUR(d.start_time) >= 18 OR HOUR(d.start_time) < 6 THEN r.night_shift_rate
        WHEN DAYOFWEEK(d.start_time) IN (1, 7) THEN r.weekend_rate
        WHEN d.duration_hours > 8 THEN r.overtime_rate
        ELSE r.base_hourly_rate
    END
WHERE d.cost_amount IS NULL
  AND r.effective_date <= DATE(d.start_time)
  AND (r.expiry_date IS NULL OR r.expiry_date >= DATE(d.start_time));
```

**2. 成本分摊**：
```sql
-- 工时成本分摊到工单
UPDATE erp_production_cost pc
JOIN (
    SELECT 
        t.production_order_id,
        SUM(d.cost_amount) as total_labor_cost
    FROM erp_work_hour_detail d
    JOIN erp_operation_task t ON d.task_id = t.id
    WHERE d.created_time >= '2024-01-01'
    GROUP BY t.production_order_id
) labor ON pc.order_id = labor.production_order_id
SET pc.labor_cost = labor.total_labor_cost;
```

#### 4.3.5 工时管理界面

**1. 工时监控看板**：
```
工时监控看板
┌─────────────────────────────────────────────────────────────┐
│ 今日工时统计 (2024-01-15)                                   │
├─────────────────────────────────────────────────────────────┤
│ 总工时：156.5小时  生产工时：142.3小时  效率：91%          │
│ 直接工时：128.5小时  间接工时：13.8小时  停机：14.2小时    │
├─────────────────────────────────────────────────────────────┤
│ 操作工效率排行：                                            │
│ 1. 张三    效率：115%  工时：8.2小时  产量：120件          │
│ 2. 李四    效率：108%  工时：8.0小时  产量：110件          │
│ 3. 王五    效率：95%   工时：8.5小时  产量：95件           │
├─────────────────────────────────────────────────────────────┤
│ 异常工时预警：                                              │
│ ⚠ MO240101-010 粗加工  实际8.5h > 标准6.0h  超时42%        │
│ ⚠ MO240102-020 精加工  停机2.5h  原因：设备故障            │
└─────────────────────────────────────────────────────────────┘
```

**2. 个人工时查询**：
```
个人工时查询 - 张三
┌─────────────────────────────────────────────────────────────┐
│ 查询期间：2024-01-01 至 2024-01-15                          │
├─────────────────────────────────────────────────────────────┤
│ 工时汇总：                                                  │
│ 总工时：120.5小时  平均效率：108%  总产量：1,250件         │
│ 直接工时：108.2小时  间接工时：12.3小时                    │
│ 加班工时：15.5小时  夜班工时：8.0小时                      │
├─────────────────────────────────────────────────────────────┤
│ 详细记录：                                                  │
│ 01-15  MO240101-010  粗加工   8.2h  效率:115%  产量:120件  │
│ 01-14  MO240100-020  精加工   7.8h  效率:105%  产量:95件   │
│ 01-13  MO240099-030  钻孔     8.5h  效率:98%   产量:180件  │
│ ...                                                         │
├─────────────────────────────────────────────────────────────┤
│ [导出Excel] [打印] [详细分析]                               │
└─────────────────────────────────────────────────────────────┘
```

#### 4.0.5 核心设计优势

- **操作简便**：工单级领料，一次性领取所需物料
- **委外物料分离**：委外工序物料留在仓库，避免浪费
- **数据结构完整**：支持未来扩展需求
- **流程清晰**：在制品和原材料/辅料分别处理
- **异常可控**：委外物料单独管理，便于追溯

### 4.1 工序开工流程

#### 4.1.1 开工前置条件检查

**系统自动检查**：
1. **前置工序状态检查**：
   ```sql
   -- 检查前置工序是否完成
   SELECT COUNT(*) as pending_count
   FROM erp_operation_task 
   WHERE production_order_id = @order_id 
   AND sequence_no < @current_sequence 
   AND status NOT IN ('已完工', '已转序');
   
   -- 如果 pending_count > 0，则不能开工
   ```

2. **物料齐套检查**：
   ```sql
   -- 检查工序所需物料是否齐套
   SELECT m.material_code, m.material_name, 
          bom.required_quantity, 
          COALESCE(inv.available_quantity, 0) as available_quantity,
          (bom.required_quantity - COALESCE(inv.available_quantity, 0)) as shortage_quantity
   FROM erp_operation_bom bom
   LEFT JOIN erp_inventory inv ON bom.material_id = inv.material_id
   LEFT JOIN erp_material m ON bom.material_id = m.id
   WHERE bom.operation_id = @operation_id
   AND bom.required_quantity > COALESCE(inv.available_quantity, 0);
   ```

3. **设备可用性检查**：
   ```sql
   -- 检查工作中心设备状态
   SELECT equipment_code, equipment_name, status
   FROM erp_equipment 
   WHERE work_center_id = @work_center_id 
   AND status IN ('故障', '维修中', '停机');
   ```

4. **人员安排检查**：
   - 检查工作中心是否有可用操作工
   - 检查操作工是否具备该工序的操作资格
   - 检查当前班次的人员安排

#### 4.1.2 开工操作界面设计

**工序任务选择界面**：
```
工序任务列表：
┌─────────────────────────────────────────────────────────────┐
│ 工作中心：机加工车间-01    操作工：张三    班次：白班        │
├─────────────────────────────────────────────────────────────┤
│ 待开工任务列表：                                            │
│ ☐ MO240101-010  五金件A  粗加工    100件  优先级:高  [开工] │
│ ☐ MO240102-020  五金件B  精加工     50件  优先级:中  [开工] │
│ ☐ MO240103-010  五金件C  钻孔       80件  优先级:低  [开工] │
├─────────────────────────────────────────────────────────────┤
│ 进行中任务：                                                │
│ ● MO240100-010  五金件D  铣削      进度:60%  预计完工:14:30  │
└─────────────────────────────────────────────────────────────┘
```

**开工确认界面**：
```
工序开工确认：
┌─────────────────────────────────────────────────────────────┐
│ 工单信息：                                                  │
│ 工单号：MO240101-010                                        │
│ 产品：五金件A  规格：50*30*10  数量：100件                  │
│ 工序：粗加工  工作中心：机加工车间-01                       │
├─────────────────────────────────────────────────────────────┤
│ 前置条件检查：                                              │
│ ✓ 前置工序：已完成                                          │
│ ✓ 物料齐套：已确认                                          │
│ ✓ 设备状态：正常                                            │
│ ✓ 人员资格：已确认                                          │
├─────────────────────────────────────────────────────────────┤
│ 物料确认：                                                  │
│ ☑ 原材料A  需要:50kg   库存:60kg   ✓                        │
│ ☑ 刀具B    需要:2把    库存:5把    ✓                        │
│ ☑ 切削液   需要:5L     库存:20L    ✓                        │
├─────────────────────────────────────────────────────────────┤
│ 设备选择：                                                  │
│ ◉ 机床001  状态:空闲  最后保养:2024-01-10                   │
│ ○ 机床002  状态:使用中                                      │
│ ○ 机床003  状态:维修中                                      │
├─────────────────────────────────────────────────────────────┤
│ 开工信息：                                                  │
│ 操作工：张三  [更换]                                        │
│ 预计工时：8小时                                             │
│ 预计完工：2024-01-15 16:00                                  │
│ 备注：[输入框]                                              │
├─────────────────────────────────────────────────────────────┤
│                    [确认开工]  [取消]                       │
└─────────────────────────────────────────────────────────────┘
```

#### 4.1.3 开工操作流程

**步骤1：任务选择**
```
操作方式：
1. 扫码开工：操作工扫描工序任务二维码
2. 手工选择：从待开工任务列表中选择
3. 系统推荐：根据优先级和工艺顺序自动推荐

任务信息展示：
- 工单基本信息（工单号、产品、数量）
- 工序信息（工序名称、工艺要求、预计工时）
- 优先级和交期信息
- 前置工序完成情况
```

**步骤2：前置条件自动检查**
```
检查项目：
1. 前置工序状态：
   - 串行工序：前道工序必须全部完工
   - 并行工序：可以同时开工
   - 部分转序：前道工序部分完工即可开工

2. 物料可用性：
   - 原材料库存检查
   - 在制品数量检查
   - 辅助材料准备情况

3. 设备状态：
   - 设备是否正常运行
   - 设备是否被其他任务占用
   - 设备保养状态检查

4. 人员资格：
   - 操作工是否具备该工序操作资格
   - 是否在当前班次
   - 是否已达到最大并行任务数
```

**步骤3：物料确认**
```
物料确认流程：
1. 系统显示工序BOM清单
2. 操作工逐项确认物料准备情况
3. 对于不足的物料：
   - 自动发起领料申请
   - 或选择替代物料
   - 或申请紧急采购

物料预留：
- 确认开工后自动预留所需物料
- 防止其他任务重复使用
- 记录物料预留时间和数量
```

**步骤4：设备分配**
```
设备选择逻辑：
1. 系统推荐最优设备：
   - 设备能力匹配度
   - 设备当前负荷情况
   - 设备维护保养状态

2. 操作工确认设备选择
3. 系统锁定设备资源
4. 更新设备状态为"使用中"
```

**步骤5：开工确认**
```
开工记录内容：
- 开工时间：系统自动记录
- 操作人员：当前登录用户
- 使用设备：选定的设备编号
- 预计完工时间：根据标准工时计算
- 开工数量：默认为任务数量，可调整
- 开工备注：操作工填写的特殊说明

数据库更新：
UPDATE erp_operation_task 
SET status = '已开工',
    actual_start_time = NOW(),
    operator_id = @operator_id,
    equipment_id = @equipment_id,
    estimated_finish_time = @estimated_time
WHERE id = @task_id;
```

#### 4.1.4 开工后系统处理

**1. 状态更新**：
```sql
-- 更新工序任务状态
UPDATE erp_operation_task 
SET status = '已开工',
    actual_start_time = NOW(),
    operator_id = @operator_id,
    equipment_id = @equipment_id
WHERE id = @task_id;

-- 更新工单状态（如果是第一个工序开工）
UPDATE erp_production_order 
SET status = '生产中',
    actual_start_time = NOW()
WHERE id = @order_id 
AND status = '已下达';
```

**2. 资源锁定**：
```sql
-- 锁定设备资源
UPDATE erp_equipment 
SET status = '使用中',
    current_task_id = @task_id,
    operator_id = @operator_id
WHERE id = @equipment_id;

-- 预留物料
INSERT INTO erp_material_reservation 
(task_id, material_id, reserved_quantity, reservation_time)
SELECT @task_id, material_id, required_quantity, NOW()
FROM erp_operation_bom 
WHERE operation_id = @operation_id;
```

**3. 计划调整**：
```sql
-- 更新后续工序的计划开始时间
UPDATE erp_operation_task 
SET planned_start_time = @current_estimated_finish_time
WHERE production_order_id = @order_id 
AND sequence_no = @current_sequence + 1;
```

**4. 消息通知**：
- 通知生产计划员：工序已开工
- 通知质检员：准备质检（如果需要）
- 通知仓库：准备下道工序物料
- 通知班组长：任务执行状态更新

#### 4.1.5 特殊情况处理

**1. 强制开工**：
```
适用场景：
- 紧急订单，需要打破正常工艺顺序
- 前置工序延误，但可以并行作业
- 特殊工艺要求

权限要求：
- 生产主管或更高级别权限
- 需要填写强制开工原因
- 系统记录风险提示

处理流程：
1. 选择"强制开工"选项
2. 输入强制开工原因
3. 系统显示风险提示
4. 主管确认并输入密码
5. 记录强制开工日志
```

**2. 批量开工**：
```
适用场景：
- 同一操作工负责多个相似工序
- 流水线作业模式
- 批量生产模式

操作流程：
1. 选择多个待开工任务
2. 系统检查批量开工的可行性
3. 统一进行前置条件检查
4. 批量分配设备和物料
5. 一次性确认开工
```

**3. 开工撤销**：
```
适用条件：
- 开工后发现条件不满足
- 设备突然故障
- 物料发现问题

撤销流程：
1. 在开工后30分钟内可以撤销
2. 释放已锁定的资源
3. 恢复任务状态为"待开工"
4. 记录撤销原因
5. 通知相关人员
```

### 4.2 工序执行
**数据记录**：
- 实际加工数量
- 物料消耗记录
- 设备使用记录
- 异常情况记录

**实时更新**：
- 任务进度百分比
- 预计完工时间
- 物料消耗情况

### 4.3 工序完工与转序
**部分完工操作**：
1. 记录部分完工数量
2. 直接创建在制品流转记录
3. 更新任务状态为"部分完工"
4. 触发下道工序开工（如果满足开工条件）

**转序条件判断**：
- 部分完工数量 ≥ 下道工序最小开工批量
- 下道工序工作中心有空闲产能
- 下道工序所需物料已准备

**全部完工操作**：
1. 记录最终完工数量（合格数量+不合格数量）
2. 记录实际工时
3. 更新任务状态为"全部完工"
4. 处理剩余流转或入库

**在制品流转管理**：
基于 `erp_wip_transfer` 表的直接流转：
```
流转单号(transfer_code)：ZX + 年月日 + 流水号
生产工单ID(production_order_id)：关联生产工单
物料ID(material_id)：在制品物料
来源工序ID(from_operation_id)：粗加工
目标工序ID(to_operation_id)：精加工
来源任务ID(from_task_id)：源工序任务
目标任务ID(to_task_id)：目标工序任务
流转数量(quantity)：50件
单位(unit)：件
批次号(batch_no)：可选，用于批次追溯
流转时间(transfer_time)：2024-01-15 14:30
流转人(transfer_by)：张三
状态(status)：已流转/已接收
备注(remarks)：第一批流转
```

**在制品库存管理**：
基于 `erp_wip_inventory` 表的在制品跟踪：
```
在制品记录包含：
- 物料ID(material_id)：在制品物料编码
- 规格ID(norms_id)：物料规格
- 生产工单ID(production_order_id)：所属工单
- 当前工序ID(current_operation_id)：当前所在工序
- 下一工序ID(next_operation_id)：下道工序
- 数量(quantity)：当前数量
- 状态(status)：在制/待转序/已转序
- 存放位置(location)：物理存放位置
- 批次号(batch_no)：批次追溯
- 生产日期(production_date)：生产日期
```

**并行生产控制**：
- 同一工单的不同工序可以并行执行
- 系统自动计算各工序的在制品数量
- 防止下道工序超前生产（不能超过上道工序累计完工数量）

## 5. 委外工序管理流程

### 5.1 委外工序触发

#### 5.1.1 委外触发条件
**自动触发时机**：
1. 前道工序完工且下道工序为委外工序
2. 委外工序的在制品数量达到委外批量要求
3. 手动触发委外申请

**触发逻辑**：
```sql
-- 检查是否需要触发委外
SELECT COUNT(*) as ready_quantity
FROM erp_wip_inventory 
WHERE production_order_id = '工单ID' 
  AND current_operation_id = '前道工序ID'
  AND status = '在制'
  AND next_operation_id = '委外工序ID';

-- 如果ready_quantity >= 委外批量，则触发委外申请
```

### 5.2 委外申请与审核流程

#### 5.2.1 委外申请单生成
**申请单内容**：
```sql
-- 委外申请单表
CREATE TABLE erp_outsourcing_request (
  id VARCHAR(32) PRIMARY KEY,
  request_code VARCHAR(50) NOT NULL COMMENT '申请单号：WW-YYYYMMDD-XXXX',
  production_order_id VARCHAR(32) NOT NULL COMMENT '生产工单ID',
  operation_id VARCHAR(32) NOT NULL COMMENT '委外工序ID',
  supplier_id VARCHAR(32) NOT NULL COMMENT '委外供应商ID',
  request_quantity DECIMAL(10,2) NOT NULL COMMENT '委外数量',
  required_date DATE NOT NULL COMMENT '要求完成日期',
  technical_requirements TEXT COMMENT '技术要求',
  estimated_cost DECIMAL(10,2) COMMENT '预估费用',
  status VARCHAR(20) DEFAULT '待审核' COMMENT '状态：待审核/已审核/已下单/已关闭',
  created_by VARCHAR(32) NOT NULL COMMENT '申请人',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  approved_by VARCHAR(32) COMMENT '审核人',
  approved_time DATETIME COMMENT '审核时间'
);
```

**申请单自动生成**：
```
委外申请单号：WW-20240115-0001
生产工单：MO240101
委外工序：热处理
委外供应商：XX热处理厂
委外数量：100件
在制品来源：前道工序（精加工）完工100件
技术要求：按图纸要求进行调质处理，硬度HRC45-50
要求完成时间：2024-01-20
预估费用：500元
```

#### 5.2.2 审核流程
**审核内容**：
1. **技术审核**：工艺工程师确认技术要求
2. **成本审核**：生产计划员确认费用合理性
3. **供应商审核**：采购部门确认供应商能力
4. **计划审核**：生产计划员确认时间安排

**审核界面**：
```
委外申请审核
┌─────────────────────────────────────────────────┐
│ 申请单号：WW-20240115-0001                      │
│ 工单信息：MO240101 - 五金件A                    │
│ 委外工序：热处理（调质）                        │
│ 委外数量：100件                                 │
│ 供应商：XX热处理厂（合格供应商）                │
│ 技术要求：硬度HRC45-50                          │
│ 预估费用：500元（5元/件）                       │
│ 要求时间：2024-01-20                            │
├─────────────────────────────────────────────────┤
│ 审核意见：                                      │
│ □ 同意  □ 驳回                                  │
│ 备注：_________________________________         │
│                                                 │
│ [提交审核]  [驳回申请]                          │
└─────────────────────────────────────────────────┘
```

### 5.3 委外采购协作流程

#### 5.3.1 委外采购单生成
**审核通过后自动生成采购单**：
```sql
-- 委外采购单表
CREATE TABLE erp_outsourcing_purchase (
  id VARCHAR(32) PRIMARY KEY,
  purchase_code VARCHAR(50) NOT NULL COMMENT '采购单号：CG-WW-YYYYMMDD-XXXX',
  request_id VARCHAR(32) NOT NULL COMMENT '关联委外申请单ID',
  supplier_id VARCHAR(32) NOT NULL COMMENT '供应商ID',
  service_description TEXT NOT NULL COMMENT '服务描述',
  quantity DECIMAL(10,2) NOT NULL COMMENT '数量',
  unit_price DECIMAL(10,2) COMMENT '单价',
  total_amount DECIMAL(10,2) COMMENT '总金额',
  delivery_date DATE NOT NULL COMMENT '要求交期',
  status VARCHAR(20) DEFAULT '待确认' COMMENT '状态：待确认/已确认/执行中/已完成',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**采购单内容**：
```
委外采购单号：CG-WW-20240115-0001
供应商：XX热处理厂
服务内容：五金件A热处理加工
技术要求：调质处理，硬度HRC45-50
加工数量：100件
单价：5元/件
总金额：500元
要求交期：2024-01-20
付款方式：月结30天
```

#### 5.3.2 采购确认流程
**采购部门操作**：
1. 联系供应商确认加工能力和交期
2. 确认价格和付款条件
3. 下达正式委外订单
4. 更新采购单状态为"已确认"

**供应商确认**：
- 供应商确认接单
- 确认具体的发料和收货安排
- 更新订单状态为"执行中"

### 5.4 委外发料与仓库协作流程

#### 5.4.1 委外发料数据流转
**委外发料包含两部分**：
1. **在制品A**：从车间发往委外厂家
2. **原材料/辅料**：从仓库直接发往委外厂家

**步骤1：查询可委外的在制品**
```sql
-- 查询前道工序完工的在制品
SELECT * FROM erp_wip_inventory 
WHERE production_order_id = 'MO240101'
  AND current_operation_id = 'OP002'  -- 前道工序（精加工）
  AND status = '在制'
  AND quantity > 0;
```

**查询结果示例**：
```
id: WIP001
production_order_id: MO240101
material_id: MAT001
current_operation_id: OP002  -- 精加工工序
next_operation_id: OP003     -- 委外热处理工序
quantity: 100
status: 在制
location: 精加工车间
batch_no: 20240115-001
```

**步骤2：查询委外工序所需原材料/辅料**
```sql
-- 查询委外工序BOM清单
SELECT 
  bom.material_id,
  m.material_code,
  m.material_name,
  bom.required_quantity * @wip_quantity as total_required_qty,
  inv.available_quantity
FROM erp_operation_bom bom
LEFT JOIN erp_material m ON bom.material_id = m.id
LEFT JOIN erp_inventory inv ON bom.material_id = inv.material_id
WHERE bom.operation_id = 'OP003'  -- 委外工序
  AND bom.material_type IN ('原材料', '辅料');
```

**步骤3：创建委外发料流转记录（在制品）**
```sql
-- 创建在制品委外发料流转记录
INSERT INTO erp_wip_transfer (
  id, transfer_code, production_order_id, material_id,
  from_operation_id, to_operation_id, quantity,
  transfer_type, transfer_time, transfer_by, status
) VALUES (
  'TF001', 'WF-20240115-0001', 'MO240101', 'MAT001',
  'OP002', 'OP003', 100,
  '委外发料', '2024-01-15 14:00:00', 'USER001', '已发料'
);
```

**步骤4：创建委外原材料/辅料出库单**
```sql
-- 创建委外原材料/辅料出库单
INSERT INTO erp_material_issue (
  id, issue_code, production_order_id, operation_id,
  issue_type, issue_to, issue_time, issue_by, status
) VALUES (
  'MI001', 'WM-20240115-0001', 'MO240101', 'OP003',
  '委外发料', 'XX热处理厂', '2024-01-15 14:00:00', 'USER001', '已出库'
);

-- 创建出库明细
INSERT INTO erp_material_issue_detail (
  issue_id, material_id, required_quantity, actual_quantity
) VALUES 
  ('MI001', 'MAT_AUX001', 5.0, 5.0),  -- 热处理剂
  ('MI001', 'MAT_AUX002', 2.0, 2.0);  -- 保护气体
```

**步骤5：更新在制品状态为委外中**
```sql
-- 更新在制品状态
UPDATE erp_wip_inventory 
SET status = '委外中',
    current_operation_id = 'OP003',  -- 委外工序
    location = 'XX热处理厂',
    updated_time = '2024-01-15 14:00:00',
    updated_by = 'USER001'
WHERE id = 'WIP001';
```

**步骤6：更新库存（原材料/辅料出库）**
```sql
-- 更新原材料/辅料库存
UPDATE erp_inventory 
SET available_quantity = available_quantity - @issue_quantity,
    updated_time = '2024-01-15 14:00:00'
WHERE material_id IN ('MAT_AUX001', 'MAT_AUX002');
```

**发料后的数据状态**：
```
erp_wip_inventory表：
id: WIP001
production_order_id: MO240101
current_operation_id: OP003  -- 已更新为委外工序
status: 委外中               -- 状态变更
location: XX热处理厂         -- 位置变更
quantity: 100

erp_wip_transfer表：
id: TF001
transfer_code: WF-20240115-0001
from_operation_id: OP002     -- 从精加工
to_operation_id: OP003       -- 到委外热处理
transfer_type: 委外发料
status: 已发料

erp_material_issue表：
id: MI001
issue_code: WM-20240115-0001
operation_id: OP003          -- 委外工序
issue_type: 委外发料
issue_to: XX热处理厂
status: 已出库

erp_inventory表：
material_id: MAT_AUX001      -- 热处理剂库存减少
available_quantity: 减少5.0
material_id: MAT_AUX002      -- 保护气体库存减少  
available_quantity: 减少2.0
```

#### 5.4.2 委外收货数据流转
**步骤1：委外收货检验合格**
委外供应商完工后，收货检验合格98件（2件不合格）

**步骤2：创建委外收货流转记录**
```sql
-- 创建委外收货流转记录
INSERT INTO erp_wip_transfer (
  id, transfer_code, production_order_id, material_id,
  from_operation_id, to_operation_id, quantity,
  transfer_type, transfer_time, transfer_by, status
) VALUES (
  'TF002', 'WS-20240119-0001', 'MO240101', 'MAT001',
  'OP003', 'OP004', 98,
  '委外收货', '2024-01-19 16:00:00', 'USER002', '已收货'
);
```

**步骤3：处理不合格品**
```sql
-- 不合格品单独记录
UPDATE erp_wip_inventory 
SET quantity = 2,
    status = '不合格',
    location = '待处理区',
    updated_time = '2024-01-19 16:00:00'
WHERE id = 'WIP001';

-- 为不合格品创建新的在制品记录ID
INSERT INTO erp_wip_inventory (
  id, production_order_id, material_id,
  current_operation_id, next_operation_id, quantity,
  status, location, batch_no
) VALUES (
  'WIP001-NG', 'MO240101', 'MAT001',
  'OP003', 'OP003', 2,
  '不合格', '待处理区', '20240115-001-NG'
);
```

**步骤4：合格品流转到下道工序**
```sql
-- 合格品流转到下道工序
UPDATE erp_wip_inventory 
SET quantity = 98,
    current_operation_id = 'OP004',  -- 下道工序（精整）
    next_operation_id = 'OP005',     -- 下下道工序
    status = '在制',
    location = '精整车间',
    updated_time = '2024-01-19 16:00:00',
    updated_by = 'USER002'
WHERE id = 'WIP001';
```

**收货后的数据状态**：
```
erp_wip_inventory表（合格品）：
id: WIP001
production_order_id: MO240101
current_operation_id: OP004  -- 已流转到下道工序
next_operation_id: OP005
status: 在制                 -- 恢复在制状态
location: 精整车间           -- 位置更新
quantity: 98                 -- 合格数量

erp_wip_inventory表（不合格品）：
id: WIP001-NG
production_order_id: MO240101
current_operation_id: OP003
status: 不合格
location: 待处理区
quantity: 2

erp_wip_transfer表（新增收货记录）：
id: TF002
transfer_code: WS-20240119-0001
from_operation_id: OP003     -- 从委外工序
to_operation_id: OP004       -- 到下道工序
transfer_type: 委外收货
quantity: 98                 -- 合格数量
status: 已收货
```

#### 5.4.3 完整的委外流转轨迹
**通过查询流转记录可以追溯完整过程**：
```sql
-- 查询某工单的委外流转轨迹
SELECT 
  t.transfer_code,
  t.transfer_type,
  op1.operation_name as from_operation,
  op2.operation_name as to_operation,
  t.quantity,
  t.transfer_time,
  u.user_name as operator
FROM erp_wip_transfer t
LEFT JOIN erp_operation op1 ON t.from_operation_id = op1.id
LEFT JOIN erp_operation op2 ON t.to_operation_id = op2.id  
LEFT JOIN erp_user u ON t.transfer_by = u.id
WHERE t.production_order_id = 'MO240101'
  AND (t.transfer_type = '委外发料' OR t.transfer_type = '委外收货')
ORDER BY t.transfer_time;
```

**查询结果**：
```
transfer_code        | transfer_type | from_operation | to_operation | quantity | transfer_time       | operator
WF-20240115-0001    | 委外发料      | 精加工         | 热处理       | 100      | 2024-01-15 14:00:00 | 张三
WS-20240119-0001    | 委外收货      | 热处理         | 精整         | 98       | 2024-01-19 16:00:00 | 李四
```

**数据变化总结**：
1. **发料时**：在制品状态从"在制"→"委外中"，位置从"精加工车间"→"XX热处理厂"
2. **收货时**：在制品状态从"委外中"→"在制"，位置从"XX热处理厂"→"精整车间"，工序从"OP003"→"OP004"
3. **流转记录**：完整记录了委外发料和收货的全过程，包括数量变化和时间节点
4. **异常处理**：不合格品单独记录，不影响合格品的正常流转

### 5.5 委外收货与质检流程

#### 5.5.1 收货通知
**供应商完工通知**：
- 供应商电话/微信通知完工
- 提供完工数量和质量情况
- 约定收货时间

#### 5.5.2 收货检验流程
**收货步骤**：
1. **外观检查**：检查数量、包装、标识
2. **抽样检验**：按检验标准进行抽检
3. **记录结果**：记录收货和检验情况
4. **处理决定**：合格入库、不合格退回

**收货界面**：
```
委外收货检验
┌─────────────────────────────────────────────────┐
│ 委外单：WW-20240115-0001                        │
│ 供应商：XX热处理厂                              │
│ 收货日期：2024-01-19                            │
├─────────────────────────────────────────────────┤
│ 收货检查：                                      │
│ 应收数量：100件                                 │
│ 实收数量：[____]件                              │
│ 包装状况：□良好 □一般 □破损                    │
│ 标识清晰：□是 □否                              │
├─────────────────────────────────────────────────┤
│ 质量检验：                                      │
│ 检验项目：硬度测试                              │
│ 抽检数量：5件                                   │
│ 检验结果：HRC46-49 ✓合格                       │
│ 检验结论：□合格 □不合格                        │
├─────────────────────────────────────────────────┤
│ 处理决定：                                      │
│ □ 全部合格入库                                  │
│ □ 部分合格，[__]件入库，[__]件退回              │
│ □ 全部不合格退回                                │
│                                                 │
│ [确认收货]  [打印收货单]                        │
└─────────────────────────────────────────────────┘
```

#### 5.5.3 进入下道工序
**合格品流转到下道工序**：
```sql
-- 更新在制品状态，流转到下道工序
UPDATE erp_wip_inventory 
SET status = '在制',
    current_operation_id = '下道工序ID',
    location = '下道工序工作中心',
    updated_time = NOW()
WHERE id IN (收货的在制品ID列表);

-- 记录委外完工
UPDATE erp_outsourcing_request 
SET status = '已完成',
    actual_completion_date = CURDATE(),
    actual_quantity = 实收数量
WHERE id = '委外申请单ID';

-- 创建工序流转记录
INSERT INTO erp_wip_transfer 
(transfer_code, production_order_id, material_id, 
 from_operation_id, to_operation_id, quantity, 
 transfer_time, transfer_by, status)
VALUES 
('ZX-' + DATE_FORMAT(NOW(), '%Y%m%d') + '-' + 流水号,
 工单ID, 物料ID, 委外工序ID, 下道工序ID, 
 实收数量, NOW(), 操作人员, '已完成');
```

**流转处理说明**：
- 委外工序完成后，在制品直接流转到下道工序
- 更新在制品的当前工序为下道工序
- 更新存放位置为下道工序的工作中心
- 如果下道工序是质检点，则状态为"待质检"
- 如果下道工序是普通工序，则状态为"在制"，等待开工

## 6. 质量检验流程

### 6.1 质检触发条件
- 工序完工且该工序有质检标识
- 委外收货
- 不合格品返工后

### 6.2 质检流程
**检验步骤**：
1. 质检员接收质检任务
2. 按照质检标准进行检验
3. 记录检验结果
4. 判定合格/不合格
5. 更新任务状态

**质检记录**：
```
质检单号：QC + 年月日 + 流水号
检验项目：尺寸、外观、硬度等
检验标准：图纸要求或工艺要求
检验结果：
- 合格数量：95
- 不合格数量：5
- 不合格原因：尺寸超差
检验结论：部分合格
处理建议：不合格品返工
```

### 6.3 质检标准管理
**标准来源**：
- 产品图纸要求
- 工艺文件规定
- 客户特殊要求

**标准内容**：
- 检验项目
- 技术要求
- 检验方法
- 抽样方案

## 7. 不合格品处理流程

### 7.1 不合格品分类与识别

#### 7.1.1 不合格品来源
1. **工序质检不合格**：工序完工后质检发现的问题
2. **委外收货不合格**：委外件收货检验发现的问题
3. **最终检验不合格**：完工前最终检验发现的问题
4. **客户退货**：产品交付后客户发现的问题

#### 7.1.2 不合格品分类
**按严重程度分类**：
- **A类（严重）**：影响产品功能和安全的缺陷
- **B类（一般）**：影响产品外观和性能的缺陷  
- **C类（轻微）**：不影响使用但不符合标准的缺陷

**按缺陷类型分类**：
- **尺寸不合格**：尺寸超出公差范围
- **外观不合格**：表面缺陷、划伤、变形等
- **材质不合格**：硬度、强度等材质问题
- **工艺不合格**：加工工艺不当导致的问题

#### 7.1.3 不合格品标识
**标识要求**：
- 红色标签标识不合格品
- 标签内容：不合格品编号、发现日期、不合格原因、责任工序
- 物理隔离：不合格品单独存放，避免混用

**标识示例**：
```
不合格品标识卡
┌─────────────────────────────────────────────────┐
│ 不合格品编号：NG-20240115-0001                  │
│ 工单号：MO240101                                │
│ 产品名称：轴承座                                │
│ 发现日期：2024-01-15                            │
│ 发现工序：精加工                                │
│ 不合格原因：孔径超差+0.05mm                     │
│ 不合格数量：5件                                 │
│ 发现人：张三                                    │
│ 状态：□待处理 ☑处理中 □已处理                  │
└─────────────────────────────────────────────────┘
```

### 7.2 不合格品处理决策

#### 7.2.1 处理方式分类
考虑到公司规模，设计四种处理方式：

1. **返工**：可以通过返工达到要求
2. **返修**：可以通过修理达到使用要求
3. **报废**：无法修复，直接报废
4. **让步接收**：虽不完全符合要求但可以使用

#### 7.2.2 处理决策流程
```
质检发现不合格 → 填写不合格品报告 → 技术评估 → 处理决策 → 执行处理 → 验证结果 → 记录归档
```

#### 7.2.3 决策权限矩阵
| 不合格类型 | A类（严重） | B类（一般） | C类（轻微） |
|------------|-------------|-------------|-------------|
| 返工决策   | 技术主管    | 车间主任    | 质检员      |
| 返修决策   | 技术主管    | 车间主任    | 质检员      |
| 报废决策   | 生产经理    | 技术主管    | 车间主任    |
| 让步接收   | 生产经理+客户确认 | 技术主管 | 车间主任    |

#### 7.2.4 决策判定规则
**返工判定**：
- 缺陷可以通过重新加工消除
- 返工成本低于重新制作成本
- 返工后能够达到图纸要求

**返修判定**：
- 缺陷无法通过返工消除但可以修理
- 修理后能够满足使用要求
- 修理成本合理

**报废判定**：
- 缺陷严重，无法修复
- 修复成本过高，不经济
- 存在安全隐患

**让步接收判定**：
- 缺陷不影响产品主要功能
- 客户同意接收（如需要）
- 价格可以适当调整

### 7.3 不合格品处理执行

#### 7.3.1 返工处理流程
**返工业务流程**：
```sql
-- 创建返工任务
INSERT INTO erp_rework_task 
(task_code, production_order_id, operation_id, ng_report_id,
 rework_reason, rework_quantity, rework_operation,
 assigned_to, task_status, created_time)
VALUES 
('RW-' + DATE_FORMAT(NOW(), '%Y%m%d') + '-' + 流水号,
 工单ID, 原工序ID, 不合格报告ID,
 '尺寸超差', 5, '重新精加工',
 操作工ID, '待开工', NOW());

-- 更新在制品状态为返工
UPDATE erp_wip_inventory 
SET status = '返工中',
    rework_flag = 1,
    updated_time = NOW()
WHERE id IN (不合格在制品ID列表);
```

**返工操作界面**：
```
返工任务执行
┌─────────────────────────────────────────────────┐
│ 返工任务：RW-20240115-0001                      │
│ 工单号：MO240101                                │
│ 原工序：精加工                                  │
│ 返工原因：孔径超差+0.05mm                       │
│ 返工数量：5件                                   │
├─────────────────────────────────────────────────┤
│ 返工方案：                                      │
│ 1. 重新夹紧工件                                 │
│ 2. 调整刀具补偿                                 │
│ 3. 重新精镗孔径                                 │
│ 4. 检查尺寸                                     │
├─────────────────────────────────────────────────┤
│ 返工记录：                                      │
│ 开工时间：[2024-01-15 14:00]                    │
│ 完工时间：[____________________]                │
│ 返工工时：[____]小时                            │
│ 返工结果：□合格 □仍不合格                      │
│ 备注：[________________________]               │
│                                                 │
│ [开始返工]  [完工确认]  [申请质检]              │
└─────────────────────────────────────────────────┘
```

**返工完成后处理**：
```sql
-- 返工完成，重新质检
UPDATE erp_rework_task 
SET task_status = '已完工',
    actual_completion_time = NOW(),
    rework_hours = 实际工时
WHERE task_code = '返工任务编号';

-- 创建重新质检任务
INSERT INTO erp_quality_inspection 
(inspection_code, production_order_id, operation_id,
 inspection_type, quantity, inspection_status)
VALUES 
('QC-' + DATE_FORMAT(NOW(), '%Y%m%d') + '-' + 流水号,
 工单ID, 工序ID, '返工复检', 5, '待检验');
```

#### 7.3.2 返修处理流程
**返修与返工的区别**：
- **返工**：重新按原工艺加工
- **返修**：采用特殊方法修理，可能改变原工艺

**返修业务流程**：
```sql
-- 创建返修任务
INSERT INTO erp_repair_task 
(task_code, production_order_id, ng_report_id,
 repair_reason, repair_method, repair_quantity,
 assigned_to, task_status, created_time)
VALUES 
('RP-' + DATE_FORMAT(NOW(), '%Y%m%d') + '-' + 流水号,
 工单ID, 不合格报告ID,
 '表面划伤', '抛光处理', 3,
 操作工ID, '待开工', NOW());
```

**返修记录要求**：
- 详细记录返修方法
- 记录返修前后对比
- 拍照记录返修过程
- 技术人员确认返修方案

#### 7.3.3 报废处理流程
**报废申请**：
```sql
-- 创建报废申请
INSERT INTO erp_scrap_application 
(application_code, production_order_id, ng_report_id,
 scrap_reason, scrap_quantity, scrap_value,
 applicant, application_status, created_time)
VALUES 
('SP-' + DATE_FORMAT(NOW(), '%Y%m%d') + '-' + 流水号,
 工单ID, 不合格报告ID,
 '严重变形无法修复', 2, 估算损失金额,
 申请人ID, '待审批', NOW());
```

**报废审批流程**：
```
申请人填写报废申请 → 车间主任初审 → 技术主管技术确认 → 
生产经理审批 → 财务确认损失 → 执行报废
```

**报废执行**：
```sql
-- 报废审批通过后执行
UPDATE erp_scrap_application 
SET application_status = '已审批',
    approved_by = 审批人ID,
    approved_time = NOW()
WHERE application_code = '报废申请编号';

-- 更新在制品状态
UPDATE erp_wip_inventory 
SET status = '已报废',
    scrap_flag = 1,
    updated_time = NOW()
WHERE id IN (报废在制品ID列表);

-- 记录报废损失
INSERT INTO erp_scrap_record 
(scrap_code, production_order_id, scrap_quantity,
 scrap_value, scrap_reason, responsibility_analysis)
VALUES 
('SC-' + DATE_FORMAT(NOW(), '%Y%m%d') + '-' + 流水号,
 工单ID, 2, 实际损失金额, '严重变形无法修复', 责任分析);
```

#### 7.3.4 让步接收处理流程
**让步接收申请**：
```sql
-- 创建让步接收申请
INSERT INTO erp_concession_application 
(application_code, production_order_id, ng_report_id,
 concession_reason, deviation_description, risk_assessment,
 customer_confirmation_required, application_status)
VALUES 
('CS-' + DATE_FORMAT(NOW(), '%Y%m%d') + '-' + 流水号,
 工单ID, 不合格报告ID,
 '外观轻微划伤，不影响功能', '表面有2mm划痕', '无功能风险',
 1, '待审批');
```

**客户确认流程**（如需要）：
```
技术评估 → 客户沟通 → 客户确认 → 价格协商 → 签署确认书 → 执行让步接收
```

**让步接收界面**：
```
让步接收申请
┌─────────────────────────────────────────────────┐
│ 申请编号：CS-20240115-0001                      │
│ 工单号：MO240101                                │
│ 不合格描述：表面有2mm划痕                       │
│ 偏差说明：外观不符合要求，但不影响功能          │
├─────────────────────────────────────────────────┤
│ 技术评估：                                      │
│ □ 不影响产品功能                                │
│ □ 不影响产品安全                                │
│ □ 客户可以接受                                  │
│ 风险评估：无功能风险                            │
├─────────────────────────────────────────────────┤
│ 客户确认：                                      │
│ 是否需要客户确认：☑是 □否                      │
│ 客户联系人：李经理                              │
│ 确认方式：□电话 ☑邮件 □现场                    │
│ 确认状态：□待确认 ☑已确认 □拒绝                │
├─────────────────────────────────────────────────┤
│ 价格调整：                                      │
│ 原价格：1000元/件                               │
│ 调整价格：950元/件（-5%）                       │
│ 调整原因：外观缺陷补偿                          │
│                                                 │
│ [提交申请]  [客户确认]  [价格协商]              │
└─────────────────────────────────────────────────┘
```

### 7.4 不合格品数据管理

#### 7.4.1 不合格品报告
**报告内容**：
```sql
-- 不合格品报告表结构
CREATE TABLE erp_ng_report (
  id INT PRIMARY KEY AUTO_INCREMENT,
  report_code VARCHAR(50) NOT NULL,           -- 报告编号
  production_order_id VARCHAR(50),            -- 工单号
  operation_id VARCHAR(50),                   -- 工序ID
  material_id VARCHAR(50),                    -- 物料ID
  ng_quantity INT,                            -- 不合格数量
  total_quantity INT,                         -- 总数量
  ng_rate DECIMAL(5,2),                       -- 不合格率
  ng_type VARCHAR(50),                        -- 不合格类型
  ng_level VARCHAR(10),                       -- 严重程度(A/B/C)
  ng_description TEXT,                        -- 不合格描述
  found_by VARCHAR(50),                       -- 发现人
  found_time DATETIME,                        -- 发现时间
  responsibility_operation VARCHAR(50),        -- 责任工序
  root_cause TEXT,                            -- 根本原因
  corrective_action TEXT,                     -- 纠正措施
  preventive_action TEXT,                     -- 预防措施
  report_status VARCHAR(20),                  -- 报告状态
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 7.4.2 处理记录跟踪
**处理过程记录**：
```sql
-- 不合格品处理记录表
CREATE TABLE erp_ng_treatment_record (
  id INT PRIMARY KEY AUTO_INCREMENT,
  ng_report_id INT,                           -- 不合格报告ID
  treatment_type VARCHAR(20),                 -- 处理类型(返工/返修/报废/让步)
  treatment_code VARCHAR(50),                 -- 处理单号
  treatment_reason TEXT,                      -- 处理原因
  treatment_method TEXT,                      -- 处理方法
  treatment_quantity INT,                     -- 处理数量
  treatment_cost DECIMAL(10,2),               -- 处理成本
  treatment_time DATETIME,                    -- 处理时间
  treatment_by VARCHAR(50),                   -- 处理人
  treatment_result VARCHAR(20),               -- 处理结果
  verification_by VARCHAR(50),                -- 验证人
  verification_time DATETIME,                 -- 验证时间
  record_status VARCHAR(20),                  -- 记录状态
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 7.4.3 统计分析功能
**不合格率统计**：
```sql
-- 按工序统计不合格率
SELECT 
  o.operation_name,
  COUNT(nr.id) as ng_count,
  SUM(nr.ng_quantity) as total_ng_quantity,
  SUM(nr.total_quantity) as total_quantity,
  ROUND(SUM(nr.ng_quantity) / SUM(nr.total_quantity) * 100, 2) as ng_rate
FROM erp_ng_report nr
JOIN erp_operation o ON nr.operation_id = o.id
WHERE nr.found_time >= '2024-01-01'
GROUP BY nr.operation_id, o.operation_name
ORDER BY ng_rate DESC;
```

**不合格原因分析**：
```sql
-- 按不合格类型统计
SELECT 
  ng_type,
  COUNT(*) as occurrence_count,
  SUM(ng_quantity) as total_ng_quantity,
  ROUND(AVG(ng_rate), 2) as avg_ng_rate
FROM erp_ng_report 
WHERE found_time >= '2024-01-01'
GROUP BY ng_type
ORDER BY occurrence_count DESC;
```

### 7.5 不合格品预防与改进

#### 7.5.1 根本原因分析
**5Why分析法**：
```
问题：孔径超差
为什么？刀具磨损
为什么？刀具使用时间过长
为什么？没有按时更换刀具
为什么？缺少刀具更换提醒
为什么？没有建立刀具管理制度

根本原因：缺少刀具管理制度
纠正措施：建立刀具使用记录和更换提醒机制
```

#### 7.5.2 纠正和预防措施
**纠正措施**（针对已发生的问题）：
- 立即处理不合格品
- 检查同批次产品
- 调整工艺参数
- 培训操作人员

**预防措施**（防止问题再次发生）：
- 完善工艺文件
- 加强过程控制
- 改进检验方法
- 建立预防机制

#### 7.5.3 持续改进机制
**改进循环**：
```
发现问题 → 分析原因 → 制定措施 → 实施改进 → 验证效果 → 标准化 → 持续监控
```

**改进效果跟踪**：
- 不合格率趋势分析
- 重复问题统计
- 改进措施有效性评估
- 成本效益分析

### 7.6 不合格品处理界面设计

#### 7.6.1 不合格品处理工作台
```
不合格品处理工作台
┌─────────────────────────────────────────────────┐
│ 待处理不合格品 (5)                              │
├─────────────────────────────────────────────────┤
│ NG-20240115-0001 | 轴承座 | 尺寸超差 | A类 | 待处理 │
│ NG-20240115-0002 | 法兰盘 | 表面划伤 | B类 | 处理中 │
│ NG-20240115-0003 | 连接件 | 硬度不足 | A类 | 待处理 │
├─────────────────────────────────────────────────┤
│ 处理统计                                        │
│ 今日处理：12件  返工：8件  报废：2件  让步：2件  │
│ 本月不合格率：2.3%  目标：<3%  ✓达标            │
├─────────────────────────────────────────────────┤
│ 快速操作                                        │
│ [批量返工]  [质量分析]  [改进建议]              │
└─────────────────────────────────────────────────┘
```

#### 7.6.2 不合格品详情处理界面
```
不合格品处理详情
┌─────────────────────────────────────────────────┐
│ 报告编号：NG-20240115-0001                      │
│ 工单：MO240101 | 产品：轴承座 | 工序：精加工     │
│ 不合格数量：5/100件 | 不合格率：5%              │
├─────────────────────────────────────────────────┤
│ 不合格描述：                                    │
│ 类型：尺寸不合格  等级：A类（严重）             │
│ 具体问题：孔径φ20+0.05mm，实测φ20.08mm         │
│ 发现人：张三  发现时间：2024-01-15 10:30        │
├─────────────────────────────────────────────────┤
│ 原因分析：                                      │
│ 直接原因：刀具磨损导致尺寸超差                  │
│ 根本原因：缺少刀具更换提醒机制                  │
│ 责任工序：精加工                                │
├─────────────────────────────────────────────────┤
│ 处理决策：                                      │
│ ○ 返工（重新精加工）  预计工时：2小时           │
│ ○ 返修（修整处理）    预计工时：1小时           │
│ ○ 报废（无法修复）    损失金额：500元           │
│ ○ 让步接收（客户确认）价格调整：-5%             │
├─────────────────────────────────────────────────┤
│ 改进措施：                                      │
│ 纠正措施：立即更换刀具，检查同批次产品          │
│ 预防措施：建立刀具使用记录，设置更换提醒        │
│                                                 │
│ [确认处理]  [打印报告]  [上传照片]              │
└─────────────────────────────────────────────────┘
```

通过以上详细的不合格品处理业务流程设计，系统能够：

1. **全面覆盖**：涵盖不合格品的发现、分类、处理、跟踪全过程
2. **权限清晰**：明确不同级别不合格品的处理权限
3. **流程规范**：标准化的处理流程，确保处理质量
4. **数据完整**：完整记录处理过程，支持分析改进
5. **持续改进**：通过数据分析推动质量持续改进

这套设计既考虑了公司规模较小的实际情况，又保证了质量管理的专业性和有效性。

## 8. 物料管理

### 8.1 物料操作流程

#### 8.1.1 领料流程
**工单下达时自动领料**：
- 系统根据BOM自动生成领料单据（document_type='领料'，sub_type='按单领料'）
- 仓库按单发料到生产现场
- 支持多次补料（document_type='补料'）

**操作步骤**：
```
1. 工单下达 → 系统自动生成领料单据
2. 仓库发料 → 更新单据状态为"已处理"
3. 物料到达现场 → 各工序按需使用
4. 不足时申请补料 → 生成补料单据
```

#### 8.1.2 退料流程
**退料场景**：
- **余料退库**：工序完工后剩余物料（sub_type='余料退库'）
- **质量退料**：发现物料质量问题（sub_type='质量退料'）
- **异常退料**：工艺变更、订单取消等（sub_type='异常退料'）

**操作步骤**：
```
1. 现场发起退料申请
2. 填写退料单据（document_type='退料'，数量、原因）
3. 仓库确认收货
4. 更新单据状态为"已处理"
```

#### 8.1.3 实时消耗跟踪
**消耗计算**：
```
当前消耗 = 领料数量 - 退料数量 + 补料数量
剩余物料 = 当前消耗 - 理论消耗
消耗率 = 当前消耗 / 理论消耗 × 100%
```

**报工时显示**：
- 通过消耗视图实时查询物料状态
- 提供消耗异常预警
- 支持直接申请补料

#### 8.1.4 补料流程
**触发场景**：
- 物料不足无法继续生产（sub_type='不足补料'）
- 质量问题需要更换物料（sub_type='质量补料'）
- 工艺调整需要额外物料（sub_type='工艺补料'）

**操作步骤**：
```
1. 工序报工时发现不足 → 点击"申请补料"
2. 填写补料申请（document_type='补料'，物料、数量、原因）
3. 生产计划员审批
4. 仓库发料到现场
5. 更新单据状态为"已处理"
```

#### 8.1.5 单据编号规则
**统一编号格式**：
```
领料单：LL-YYYYMMDD-XXXX
退料单：TL-YYYYMMDD-XXXX  
补料单：BL-YYYYMMDD-XXXX
```

**编号生成逻辑**：
```sql
-- 单据编号生成函数示例
FUNCTION generate_document_code(doc_type VARCHAR(10))
RETURNS VARCHAR(50)
BEGIN
  DECLARE prefix VARCHAR(5);
  DECLARE seq_num INT;
  
  -- 根据单据类型确定前缀
  CASE doc_type
    WHEN '领料' THEN SET prefix = 'LL-';
    WHEN '退料' THEN SET prefix = 'TL-';
    WHEN '补料' THEN SET prefix = 'BL-';
  END CASE;
  
  -- 获取当日序号
  SELECT COALESCE(MAX(CAST(RIGHT(document_code, 4) AS UNSIGNED)), 0) + 1
  INTO seq_num
  FROM erp_material_document 
  WHERE document_type = doc_type 
    AND DATE(document_date) = CURDATE();
  
  RETURN CONCAT(prefix, DATE_FORMAT(NOW(), '%Y%m%d'), '-', LPAD(seq_num, 4, '0'));
END;
```

### 8.2 物料消耗记录
**记录内容**：
- 消耗时间
- 消耗数量
- 消耗工序
- 操作人员

**记录方式**：
- 通过领料单、退料单自动计算
- 工序报工时可记录实际消耗
- 支持消耗异常分析

### 8.3 物料数据管理
**数据来源**：
- 领料单：记录物料发放
- 退料单：记录物料退回
- 报工记录：记录实际消耗

**数据统计**：
- 实时计算物料消耗情况
- 提供消耗率分析
- 支持成本核算对接

## 9. 数据流转

### 9.1 与现有系统的数据交互
```
工程方案系统 → 工艺流程数据 → 生产过程系统
生产工单系统 → 工单信息 → 生产过程系统
生产过程系统 → 完工信息 → 完工库单系统
```

### 9.2 关键数据同步点
- 工序任务生成时同步工艺数据
- 工序完工时同步进度数据
- 质检完成时同步质量数据
- 在制品流转时同步流转数据

### 9.3 并行生产数据管理
**在制品数量跟踪**（基于 `erp_wip_inventory` 表）：
```sql
-- 查询各工序当前在制品数量
SELECT current_operation_id, SUM(quantity) as wip_quantity 
FROM erp_wip_inventory 
WHERE production_order_id = '工单ID' AND status = '在制'
GROUP BY current_operation_id;

-- 查询工序累计完工数量（通过流转记录统计）
SELECT from_operation_id, SUM(quantity) as completed_quantity
FROM erp_wip_transfer 
WHERE production_order_id = '工单ID' AND status = '已接收'
GROUP BY from_operation_id;
```

**数据一致性控制**：
- 通过 `erp_wip_inventory` 表实时跟踪各工序在制品
- 通过 `erp_wip_transfer` 表记录所有流转历史
- 系统自动校验：下道工序在制品数量 ≤ 上道工序累计完工数量
- 实时更新各工序的可开工数量

**在制品流转业务流程**：
1. 工序部分完工时，更新 `erp_wip_inventory` 表中的数量
2. 创建 `erp_wip_transfer` 流转记录
3. 更新在制品的 `current_operation_id` 到下道工序
4. 触发下道工序任务的开工条件检查

## 10. 异常处理

### 10.1 常见异常情况
- 设备故障导致工序中断
- 物料短缺无法继续生产
- 委外供应商延期交货
- 质检不合格率过高

### 10.2 异常处理机制
- 异常及时记录和上报
- 自动调整后续工序计划
- 异常原因分析和改进

## 11. 单据状态管理与线下操作

### 11.1 物料单据状态管理

#### 11.1.1 领料单状态流转
**状态定义**：
```
待处理 → 部分发料 → 已发料 → 已确认 → 已关闭
```

**状态说明与线下操作**：

1. **待处理**：
   - **触发条件**：工单下达时系统自动生成
   - **线下操作**：仓库管理员查看待发料清单，准备物料
   - **操作界面**：仓库发料工作台显示待处理领料单
   - **处理动作**：仓库管理员点击"开始发料"

2. **部分发料**：
   - **触发条件**：仓库开始发料但未全部发完
   - **线下操作**：仓库分批发料到生产现场
   - **操作界面**：扫码发料，记录实际发料数量
   - **处理动作**：继续发料或标记缺料原因

3. **已发料**：
   - **触发条件**：所有物料已从仓库发出
   - **线下操作**：物料运输到生产现场
   - **操作界面**：生产现场确认收料界面
   - **处理动作**：现场负责人确认收到物料

4. **已确认**：
   - **触发条件**：生产现场确认收到物料
   - **线下操作**：物料正式投入生产使用
   - **操作界面**：工序任务界面显示物料可用
   - **处理动作**：开始生产或发现问题申请退料

5. **已关闭**：
   - **触发条件**：工单完工或异常关闭
   - **线下操作**：清理相关物料，处理剩余物料
   - **操作界面**：工单关闭时自动关闭相关领料单
   - **处理动作**：无需操作，系统自动处理

**异常状态处理**：
- **缺料暂停**：库存不足时的临时状态
- **质量异常**：发现物料质量问题时的状态
- **已取消**：工单取消时领料单的终止状态

#### 11.1.2 退料单状态流转
**状态定义**：
```
待审核 → 已审核 → 待收料 → 已收料 → 已入库 → 已关闭
```

**状态说明与线下操作**：

1. **待审核**：
   - **触发条件**：现场申请退料
   - **线下操作**：生产现场填写退料申请，说明退料原因
   - **操作界面**：退料申请界面，选择物料、数量、原因
   - **处理动作**：提交申请等待审核

2. **已审核**：
   - **触发条件**：生产计划员或车间主任审核通过
   - **线下操作**：审核人员确认退料的合理性
   - **操作界面**：退料审核界面，查看申请详情
   - **处理动作**：同意退料或驳回申请

3. **待收料**：
   - **触发条件**：退料申请审核通过
   - **线下操作**：现场准备退料，联系仓库收料
   - **操作界面**：仓库收料工作台显示待收料清单
   - **处理动作**：仓库安排人员到现场收料

4. **已收料**：
   - **触发条件**：仓库人员确认收到退料
   - **线下操作**：仓库检查退料质量和数量
   - **操作界面**：退料收货界面，扫码确认收料
   - **处理动作**：确认收料或标记质量问题

5. **已入库**：
   - **触发条件**：退料检查合格并入库
   - **线下操作**：退料重新上架，更新库存
   - **操作界面**：库存管理界面更新库存数量
   - **处理动作**：完成退料流程

6. **已关闭**：
   - **触发条件**：退料流程完成或异常关闭
   - **线下操作**：清理相关记录，归档单据
   - **操作界面**：系统自动关闭
   - **处理动作**：无需操作

**异常状态处理**：
- **审核驳回**：退料申请不合理时的状态
- **质量不合格**：退料质量有问题时的状态
- **已取消**：申请人取消退料申请时的状态

#### 11.1.3 补料单状态流转
**状态定义**：
```
待审核 → 已审核 → 待发料 → 已发料 → 已确认 → 已关闭
```

**状态说明与线下操作**：

1. **待审核**：
   - **触发条件**：现场申请补料
   - **线下操作**：操作工发现物料不足，申请补料
   - **操作界面**：工序报工界面的"申请补料"功能
   - **处理动作**：填写补料原因和数量，提交申请

2. **已审核**：
   - **触发条件**：生产计划员审核通过
   - **线下操作**：审核人员确认补料的必要性
   - **操作界面**：补料审核界面，查看生产进度和物料消耗
   - **处理动作**：同意补料或建议调整用量

3. **待发料**：
   - **触发条件**：补料申请审核通过
   - **线下操作**：仓库准备补料物料
   - **操作界面**：仓库发料工作台显示紧急补料任务
   - **处理动作**：优先处理补料，避免停产

4. **已发料**：
   - **触发条件**：仓库发出补料
   - **线下操作**：补料送达生产现场
   - **操作界面**：现场确认收料界面
   - **处理动作**：现场确认收到补料

5. **已确认**：
   - **触发条件**：现场确认收到补料
   - **线下操作**：补料投入生产使用
   - **操作界面**：工序任务界面更新可用物料
   - **处理动作**：继续生产

6. **已关闭**：
   - **触发条件**：补料使用完成或工单完工
   - **线下操作**：清理补料记录
   - **操作界面**：系统自动关闭
   - **处理动作**：无需操作

### 11.2 工序任务单状态管理

#### 11.2.1 工序任务状态流转
**根据工序类型的不同状态流转**：

**A. 非质检点工序状态流转**：
```
待生产 → 生产中 → 已完工 → 已关闭
```

**B. 质检点工序状态流转**：
```
待生产 → 生产中 → 待质检 → 已完工 → 已关闭
```

**状态说明与线下操作**：

1. **待生产**：
   - **触发条件**：工单下达后自动生成工序任务
   - **线下操作**：操作工查看任务清单，准备开工
   - **操作界面**：工序任务列表，显示待生产任务
   - **处理动作**：检查前置条件，点击"开始生产"

2. **生产中**：
   - **触发条件**：操作工确认开始生产
   - **线下操作**：持续生产，可能有中途报工
   - **操作界面**：工序执行界面，可报告进度
   - **处理动作**：按工艺要求进行生产，完成后点击"完工"

3. **待质检**（仅质检点工序）：
   - **触发条件**：质检点工序生产完成
   - **线下操作**：通知质检员进行检验
   - **操作界面**：质检任务列表显示待检项目
   - **处理动作**：质检员按标准进行检验
   - **适用工序**：关键工序、精加工工序、委外收货等

4. **已完工**：
   - **触发条件**：
     - 非质检点工序：生产完成即可
     - 质检点工序：质检合格后才能完工
   - **线下操作**：清理工位，记录完工数量
   - **操作界面**：完工确认界面
   - **处理动作**：确认完工数量，系统自动更新在制品状态

5. **已关闭**：
   - **触发条件**：工序任务完全结束或工单关闭
   - **线下操作**：归档记录
   - **操作界面**：系统自动关闭
   - **处理动作**：无需操作

**异常状态处理**：
- **暂停**：设备故障或物料问题时的临时状态
- **返工**：质检不合格需要返工时的状态（仅质检点工序）
- **取消**：工单取消时工序任务的终止状态

### 11.3 委外单据状态管理

#### 11.3.1 委外申请单状态流转
**状态定义**：
```
待审核 → 已审核 → 待发料 → 已发料 → 委外中 → 待收货 → 已收货 → 已关闭
```

**状态说明与线下操作**：

1. **待审核**：
   - **触发条件**：前道工序完工且下道为委外工序
   - **线下操作**：生产计划员评估委外必要性
   - **操作界面**：委外申请审核界面
   - **处理动作**：确认委外供应商和要求

2. **已审核**：
   - **触发条件**：委外申请审核通过
   - **线下操作**：联系委外供应商，确认加工能力
   - **操作界面**：委外订单生成界面
   - **处理动作**：下达委外订单

3. **待发料**：
   - **触发条件**：委外订单确认
   - **线下操作**：准备委外发料（在制品+辅料）
   - **操作界面**：委外发料清单界面
   - **处理动作**：按清单准备发料

4. **已发料**：
   - **触发条件**：物料发送给委外供应商
   - **线下操作**：物料运输到委外厂家
   - **操作界面**：委外跟踪界面
   - **处理动作**：供应商确认收料

5. **委外中**：
   - **触发条件**：供应商开始委外加工
   - **线下操作**：定期跟踪委外进度
   - **操作界面**：委外进度跟踪界面
   - **处理动作**：电话跟踪或现场检查

6. **待收货**：
   - **触发条件**：供应商通知加工完成
   - **线下操作**：安排收货检验
   - **操作界面**：委外收货界面
   - **处理动作**：检查数量和外观

7. **已收货**：
   - **触发条件**：委外产品收货入库
   - **线下操作**：安排质检或直接转序
   - **操作界面**：质检任务或转序界面
   - **处理动作**：按流程继续处理

8. **已关闭**：
   - **触发条件**：委外流程完全结束
   - **线下操作**：结算委外费用
   - **操作界面**：委外结算界面
   - **处理动作**：财务结算

### 11.4 质检单据状态管理

#### 11.4.1 质检单状态流转
**状态定义**：
```
待检验 → 检验中 → 待审核 → 已审核 → 已处理 → 已关闭
```

**状态说明与线下操作**：

1. **待检验**：
   - **触发条件**：工序完工或委外收货触发质检
   - **线下操作**：质检员查看待检清单
   - **操作界面**：质检任务列表
   - **处理动作**：领取质检任务

2. **检验中**：
   - **触发条件**：质检员开始检验
   - **线下操作**：按检验标准进行检测
   - **操作界面**：质检记录界面
   - **处理动作**：记录检验数据

3. **待审核**：
   - **触发条件**：质检员完成检验
   - **线下操作**：质检主管审核检验结果
   - **操作界面**：质检审核界面
   - **处理动作**：确认检验结论

4. **已审核**：
   - **触发条件**：质检结果审核通过
   - **线下操作**：根据检验结果进行处理
   - **操作界面**：不合格品处理界面（如有）
   - **处理动作**：合格品继续流程，不合格品特殊处理

5. **已处理**：
   - **触发条件**：质检结果已处理完成
   - **线下操作**：更新产品状态
   - **操作界面**：生产进度界面更新
   - **处理动作**：继续后续流程

6. **已关闭**：
   - **触发条件**：质检流程完全结束
   - **线下操作**：归档质检记录
   - **操作界面**：系统自动关闭
   - **处理动作**：无需操作

### 11.5 单据状态监控与异常处理

#### 11.5.1 状态监控机制
**实时监控**：
- 各类单据的状态分布统计
- 超时未处理的单据预警
- 异常状态的单据提醒

**监控界面**：
```
单据状态监控看板
┌─────────────────────────────────────────────────┐
│ 今日单据处理情况                                │
├─────────────────────────────────────────────────┤
│ 领料单：待处理(5) 处理中(12) 已完成(28)        │
│ 退料单：待审核(2) 处理中(3)  已完成(8)         │
│ 补料单：待审核(1) 处理中(2)  已完成(5)         │
│ 工序任务：待开工(15) 进行中(25) 已完工(45)     │
│ 委外单：委外中(8) 待收货(3) 已完成(12)         │
│ 质检单：待检验(6) 检验中(4) 已完成(18)         │
├─────────────────────────────────────────────────┤
│ 异常提醒：                                      │
│ ⚠ 领料单LL-20240115-0003 超时未处理(2小时)     │
│ ⚠ 委外单WF-20240110-0001 超期未收货(3天)       │
└─────────────────────────────────────────────────┘
```

#### 11.5.2 异常处理流程
**超时处理**：
- 自动提醒相关责任人
- 升级通知上级管理者
- 记录异常原因和处理结果

**状态回退**：
- 支持特殊情况下的状态回退
- 需要相应权限和审批
- 记录回退原因和操作人

**强制关闭**：
- 异常情况下的强制关闭机制
- 需要高级权限
- 必须填写关闭原因

## 12. 报表和统计

### 12.1 生产进度报表
- 工单完成情况
- 工序执行进度
- 委外加工进度

### 12.2 质量统计报表
- 质检合格率
- 不合格原因分析
- 返工率统计

### 12.3 效率分析报表
- 工序工时分析
- 设备利用率
- 人员效率分析