# 物料单据管理页面设计

## 1. 物料单据状态管理（按业务流程设计文档）

### 1.1 领料单状态流转
```
待处理 → 部分发料 → 已发料 → 已确认 → 已关闭
```

### 1.2 退料单状态流转
```
待审核 → 已审核 → 待收料 → 已收料 → 已入库 → 已关闭
```

### 1.3 补料单状态流转
```
待审核 → 已审核 → 待发料 → 已发料 → 已确认 → 已关闭
```

**状态说明**：
- **待处理/待审核**：单据已生成，等待处理或审核
- **部分发料**：领料单部分发料完成
- **已发料/已审核**：发料完成或审核通过
- **待收料**：退料单等待仓库收料
- **已收料/已确认**：收料完成或发料确认
- **已入库**：退料已入库
- **已关闭**：单据完全结束

## 2. 物料单据列表管理界面

### 2.1 物料单据综合列表
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 物料单据管理                          [新建领料] [新建退料] [新建补料]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 单据类型：[全部▼] 单据状态：[全部▼] 工单号：[_______] 日期：[今日▼]       │
│ 单据编号：[_______] 申请人：[全部▼]                   [搜索] [重置]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 物料单据列表                                                                │
│ ☐ 单据编号      类型  工单号      申请人  申请时间      状态    操作        │
│ ☐ LL-20240115-001 领料 MO240115001 张师傅 2024-01-15 08:00 🟡待处理 [发料] │
│ ☐ BL-20240115-002 补料 MO240115002 李师傅 2024-01-15 10:30 🔵已审核 [发料] │
│ ☐ TL-20240115-003 退料 MO240115003 王师傅 2024-01-15 14:20 🟠待审核 [审核] │
│ ☐ LL-20240115-004 领料 MO240115004 赵师傅 2024-01-15 16:00 🟢已发料 [确认] │
│ ☐ TL-20240115-005 退料 MO240115005 陈师傅 2024-01-15 17:30 🔴已收料 [入库] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 单据统计（按类型）                                                          │
│ 领料单：待处理(8) 发料中(15) 已完成(42)  退料单：待审核(3) 处理中(5) 已完成(12) │
│ 补料单：待审核(2) 发料中(4) 已完成(18)   今日处理：68单  完成率：85.2%     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 3. 领料单管理

### 3.1 领料单创建界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📤 创建领料单                                               [保存] [提交]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 单据编号：LL-20240115-006（自动生成）  单据类型：领料单                    │
│ 工单号：[MO240115001▼]  工序任务：[全部工序▼]                             │
│ 申请人：[张师傅▼]  申请部门：[生产部▼]  申请时间：[2024-01-15 08:00]      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 物料明细                                                                    │
│ 物料名称      规格      计划用量  库存数量  申请数量  单位  备注    操作    │
│ 钢材Q235     Φ20×6000  100kg    500kg    100kg    kg   [____] [删除]      │
│ 切削液CL-1   5L装       2桶      10桶     2桶      桶   [____] [删除]      │
│ 刀具T01      硬质合金   5把      20把     5把      把   [____] [删除]      │
│                                                         [添加物料]         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 自动计算汇总                                                                │
│ 总申请数量：107项  总估算金额：2,850元  库存充足率：100%                   │
│ 备注：[按BOM清单自动生成，库存充足]                                        │
│                                              [取消] [保存草稿] [提交审核]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 领料单发料界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📦 领料单发料 - LL-20240115-001                             [确认发料]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 单据信息                                                                    │
│ 单据编号：LL-20240115-001  工单号：MO240115001  申请人：张师傅              │
│ 申请时间：2024-01-15 08:00  当前状态：🟡待处理                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 发料明细                                                                    │
│ 物料名称      规格      申请数量  库存数量  发料数量  批次号    备注        │
│ 钢材Q235     Φ20×6000  100kg    500kg    [100]kg   B240115001 [____]      │
│ 切削液CL-1   5L装       2桶      10桶     [2]桶     B240115002 [____]      │
│ 刀具T01      硬质合金   5把      20把     [5]把     B240115003 [____]      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 发料信息                                                                    │
│ 发料人：[仓管员A▼]  发料时间：[2024-01-15 09:00]                          │
│ 发料地点：[生产车间A▼]  运输方式：[叉车▼]                                 │
│ 发料备注：[_________________________________________________]               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 发料前检查                                                                  │
│ ✅ 库存充足检查：所有物料库存充足                                          │
│ ✅ 质量状态检查：所有物料质量合格                                          │
│ ✅ 批次管理检查：批次信息完整                                              │
│ ⚠️  运输准备检查：建议使用防护包装                                         │
│                                              [取消] [部分发料] [确认发料]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. 退料单管理

### 4.1 退料单创建界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📥 创建退料单                                               [保存] [提交]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 单据编号：TL-20240115-006（自动生成）  单据类型：退料单                    │
│ 工单号：[MO240115001▼]  工序任务：[下料工序▼]                             │
│ 申请人：[张师傅▼]  申请部门：[生产部▼]  申请时间：[2024-01-15 16:00]      │
│ 退料原因：[余料退库▼]  关联领料单：[LL-20240115-001▼]                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 退料明细                                                                    │
│ 物料名称      规格      领料数量  已用数量  退料数量  批次号    退料原因    │
│ 钢材Q235     Φ20×6000  100kg    80kg     [20]kg    B240115001 余料退库    │
│ 切削液CL-1   5L装       2桶      1桶      [1]桶     B240115002 余料退库    │
│ 刀具T01      硬质合金   5把      3把      [2]把     B240115003 余料退库    │
│                                                         [添加退料]         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 退料说明                                                                    │
│ 退料类型：余料退库  退料总数量：23项  预计退料金额：580元                  │
│ 退料原因：[工序完工，剩余物料退回仓库]                                      │
│ 质量状态：[合格▼]  包装状态：[完好▼]                                      │
│                                              [取消] [保存草稿] [提交审核]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 退料单审核界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✅ 退料单审核 - TL-20240115-003                             [审核通过]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 申请信息                                                                    │
│ 单据编号：TL-20240115-003  工单号：MO240115003  申请人：王师傅              │
│ 申请时间：2024-01-15 14:20  退料原因：质量异常                             │
│ 关联领料单：LL-20240115-003  当前状态：🟠待审核                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 退料明细审核                                                                │
│ 物料名称      规格      退料数量  退料原因    质量状态  审核意见    审核结果 │
│ 轴承6205     内径25mm   10个     质量不合格  不合格    [同意退料] ✅通过   │
│ 密封圈O-25   橡胶材质   20个     规格不符    不合格    [同意退料] ✅通过   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 审核信息                                                                    │
│ 审核人：[质检主管▼]  审核时间：[2024-01-15 15:00]                         │
│ 审核结果：[通过▼]  处理方式：[退回供应商▼]                                │
│ 审核意见：[_________________________________________________]               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 后续处理                                                                    │
│ 🔄 自动操作：审核通过后自动通知仓库收料                                   │
│ 📋 质量记录：自动记录质量异常信息                                          │
│ 📞 供应商通知：自动发送质量异常通知                                        │
│                                              [驳回] [审核通过] [转质检]     │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. 补料单管理

### 5.1 补料单创建界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ➕ 创建补料单                                               [保存] [提交]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 单据编号：BL-20240115-006（自动生成）  单据类型：补料单                    │
│ 工单号：[MO240115002▼]  工序任务：[粗加工工序▼]                           │
│ 申请人：[李师傅▼]  申请部门：[生产部▼]  申请时间：[2024-01-15 10:30]      │
│ 补料原因：[消耗超标▼]  关联领料单：[LL-20240115-002▼]                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 补料明细                                                                    │
│ 物料名称      规格      原领数量  已用数量  剩余数量  补料数量  补料原因    │
│ 切削液CL-1   5L装       5桶      5桶      0桶      [3]桶    消耗超标      │
│ 刀具T02      陶瓷刀具   3把      3把      0把      [2]把    磨损更换      │
│                                                         [添加补料]         │
├─────────────────────────────────────────────────────────────────────────────┤
│ 补料原因分析                                                                │
│ 补料类型：消耗超标  补料总数量：5项  预计补料金额：450元                   │
│ 超标原因：[加工难度超预期，刀具磨损严重，切削液消耗增加]                    │
│ 预防措施：[调整工艺参数，优化切削条件]                                      │
│ 影响评估：[延期0.5天，增加成本450元]                                       │
│                                              [取消] [保存草稿] [提交审核]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 5.2 补料单审核界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✅ 补料单审核 - BL-20240115-002                             [审核通过]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 申请信息                                                                    │
│ 单据编号：BL-20240115-002  工单号：MO240115002  申请人：李师傅              │
│ 申请时间：2024-01-15 10:30  补料原因：消耗超标                             │
│ 关联领料单：LL-20240115-002  当前状态：🔵待审核                           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 补料合理性分析                                                              │
│ 原计划消耗：切削液5桶，刀具3把  实际消耗：切削液5桶，刀具3把                │
│ 补料申请：切削液3桶，刀具2把    超标比例：切削液60%，刀具67%                │
│ 超标原因：加工难度超预期，材料硬度高于预期                                  │
│ 技术确认：✅工艺工程师确认合理  成本影响：增加450元（可接受范围）           │
├─────────────────────────────────────────────────────────────────────────────┤
│ 审核决策                                                                    │
│ 审核人：[生产主管▼]  审核时间：[2024-01-15 11:00]                         │
│ 审核结果：[通过▼]  批准数量：[按申请数量▼]                                │
│ 审核意见：[_________________________________________________]               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 后续安排                                                                    │
│ 🚀 紧急程度：[一般▼]  要求到货时间：[2024-01-15 14:00]                    │
│ 📦 发料安排：审核通过后立即安排发料                                        │
│ 📊 成本记录：自动记录到工单成本                                            │
│                                              [驳回] [审核通过] [部分通过]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 6. 物料单据详情管理

### 6.1 物料单据详情界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 物料单据详情 - LL-20240115-001                       [编辑] [打印]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 单据编号：LL-20240115-001  单据类型：领料单  当前状态：🟢已发料            │
│ 工单号：MO240115001  工序任务：下料工序  申请人：张师傅                    │
│ 申请时间：2024-01-15 08:00  发料时间：2024-01-15 09:00                     │
│ 申请部门：生产部  发料人：仓管员A  发料地点：生产车间A                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 物料明细                                                                    │
│ 物料名称      规格      申请数量  发料数量  差异  批次号    发料状态        │
│ 钢材Q235     Φ20×6000  100kg    100kg    0kg   B240115001 ✅已发料        │
│ 切削液CL-1   5L装       2桶      2桶      0桶   B240115002 ✅已发料        │
│ 刀具T01      硬质合金   5把      5把      0把   B240115003 ✅已发料        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 状态流转记录                                                                │
│ 时间              状态变更        操作人    备注                            │
│ 2024-01-15 08:00  创建单据        张师傅    按BOM自动生成                  │
│ 2024-01-15 08:30  提交审核        张师傅    申请发料                        │
│ 2024-01-15 08:45  审核通过        生产主管  库存充足，同意发料              │
│ 2024-01-15 09:00  开始发料        仓管员A   开始准备物料                    │
│ 2024-01-15 09:30  发料完成        仓管员A   全部物料已发放到现场            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 关联信息                                                                    │
│ 关联工单：MO240115001 - 轴承座加工  工单状态：生产中                      │
│ 关联BOM：BOM240115001  BOM版本：V1.0                                       │
│ 后续单据：TL-20240115-003（退料单）  BL-20240115-002（补料单）             │
└─────────────────────────────────────────────────────────────────────────────┘
```



## 7. 物料单据批量操作

### 7.1 批量发料界面
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📦 批量发料处理                                         [全选] [批量发料]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 待发料单据列表                                                              │
│ ☑ 单据编号      工单号      申请人  申请时间      优先级  预计发料时间      │
│ ☑ LL-20240115-001 MO240115001 张师傅 2024-01-15 08:00 🔴紧急 09:00        │
│ ☑ LL-20240115-004 MO240115004 赵师傅 2024-01-15 16:00 🟡一般 17:00        │
│ ☐ LL-20240115-007 MO240115007 孙师傅 2024-01-15 18:00 🟢普通 明日08:00    │
│ ☑ BL-20240115-002 MO240115002 李师傅 2024-01-15 10:30 🔴紧急 11:00        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 批量发料信息                                                                │
│ 选中单据：3个  涉及工单：3个  总物料项：25项                               │
│ 发料人：[仓管员A▼]  发料时间：[2024-01-15 09:00]                          │
│ 发料地点：[生产车间A▼]  运输方式：[叉车▼]                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 库存检查结果                                                                │
│ ✅ 钢材Q235：库存充足（需要200kg，库存500kg）                              │
│ ✅ 切削液CL-1：库存充足（需要8桶，库存15桶）                               │
│ ⚠️  刀具T01：库存紧张（需要12把，库存13把）                                │
│ 🔴 轴承6205：库存不足（需要50个，库存30个）                               │
├─────────────────────────────────────────────────────────────────────────────┤
│ 发料策略                                                                    │
│ 🟢 充足物料：立即发料  ⚠️ 紧张物料：优先紧急单据  🔴 不足物料：部分发料    │
│ 建议处理：先发放库存充足的物料，不足物料单独处理                            │
│                                              [取消] [部分发料] [批量发料]   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.2 物料单据统计分析
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📈 物料单据统计分析                                     [导出] [打印]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 时间范围：[本月▼] 2024年1月  单据类型：[全部▼]                            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 单据数量统计                                                                │
│ 单据类型  总数量  已完成  处理中  异常数  完成率  平均处理时间              │
│ 领料单    156个   142个   12个    2个    91.0%   2.3小时                  │
│ 退料单    45个    38个    5个     2个    84.4%   4.1小时                  │
│ 补料单    32个    28个    3个     1个    87.5%   3.2小时                  │
│ 合计      233个   208个   20个    5个    89.3%   2.8小时                  │

├─────────────────────────────────────────────────────────────────────────────┤
│ 异常单据分析                                                                │
│ 异常类型      数量  占比   主要原因                        改进建议        │
│ 超时未处理    3个   1.3%   人员不足，优先级不明确          增加人员配置    │
│ 库存不足      2个   0.9%   采购计划不准确                  优化采购计划    │
│ 质量异常      2个   0.9%   供应商质量问题                  加强质量检验    │
├─────────────────────────────────────────────────────────────────────────────┤
│ 效率改进建议                                                                │
│ 🎯 优化建议：                                                              │
│ 1. 建立物料消耗预警机制，提前识别异常消耗                                  │
│ 2. 优化审核流程，缩短补料单审核时间                                        │
│ 3. 加强供应商管理，减少质量异常退料                                        │
│ 4. 完善库存预警，避免缺料影响生产                                          │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 8. 数据库字段映射

### 8.1 erp_pick_header 表字段对应
```
界面元素                    数据库字段                  说明
单据编号                    default_number             单据唯一编号
单据类型                    type                       19-领料单，20-补料单，21-退料单
仓库                        depot_id                   发料仓库
单据时间                    oper_time                  申请/创建时间
总金额                      total_price                单据总金额
备注                        remark                     单据备注
申请部门                    department_id              申请部门
单据状态                    status                     0-未审核，1-审核中，2-审核通过，3-审核拒绝
审核人                      examine_id                 审核人员
审核意见                    examine_content            审核意见内容
审核时间                    examine_time               审核完成时间
关联工单                    work_order_id              关联的生产工单ID
关联工序                    task_id                    关联的工序任务ID
```

### 8.2 erp_pick_child 表字段对应
```
界面元素                    数据库字段                  说明
物料ID                      material_id                物料标识
规格ID                      norms_id                   物料规格
申请/退料数量               need_num                   数量
单价                        unit_price                 物料单价
总价                        all_price                  行总价
备注                        remark                     行备注
申请人/退料人               applicant                  操作人员
批次ID                      batch_id                   物料批次
批次编码                    batch_code                 批次编码
关联工单                    work_order_id              关联的生产工单ID
关联工序                    task_id                    关联的工序任务ID
```

## 9. 状态流转SQL示例

### 9.1 领料单状态流转
```sql
-- 创建领料单
INSERT INTO erp_pick_header (id, default_number, type, work_order_id, task_id,
    depot_id, oper_time, status, total_price, remark, department_id)
VALUES ('PH001', 'LL-20240115-001', 19, 'WO001', 'PT001',
    'D001', NOW(), 0, 2850.00, '按BOM清单自动生成', 'DEPT001');

-- 提交审核（待处理 → 审核中）
UPDATE erp_pick_header
SET status = 1, examine_time = NOW()
WHERE id = 'PH001';

-- 审核通过（审核中 → 已审核）
UPDATE erp_pick_header
SET status = 2, examine_id = 'USER001', examine_content = '库存充足，同意发料',
    examine_time = NOW()
WHERE id = 'PH001';

-- 开始发料（已审核 → 发料中）
UPDATE erp_pick_header
SET status = 3, remark = CONCAT(remark, ' | 开始发料')
WHERE id = 'PH001';

-- 发料完成（发料中 → 已发料）
UPDATE erp_pick_header
SET status = 4, remark = CONCAT(remark, ' | 发料完成')
WHERE id = 'PH001';

-- 确认收料（已发料 → 已确认）
UPDATE erp_pick_header
SET status = 5, remark = CONCAT(remark, ' | 现场确认收料')
WHERE id = 'PH001';

-- 关闭单据（已确认 → 已关闭）
UPDATE erp_pick_header
SET status = 6, remark = CONCAT(remark, ' | 单据关闭')
WHERE id = 'PH001';
```

### 9.2 退料单状态流转
```sql
-- 创建退料单
INSERT INTO erp_pick_header (id, default_number, type, work_order_id, task_id,
    depot_id, oper_time, status, total_price, remark, department_id)
VALUES ('PH002', 'TL-20240115-003', 21, 'WO001', 'PT001',
    'D001', NOW(), 0, 580.00, '余料退库', 'DEPT001');

-- 提交审核（待审核 → 审核中）
UPDATE erp_pick_header
SET status = 1, examine_time = NOW()
WHERE id = 'PH002';

-- 审核通过（审核中 → 已审核）
UPDATE erp_pick_header
SET status = 2, examine_id = 'USER002', examine_content = '退料合理，同意退库',
    examine_time = NOW()
WHERE id = 'PH002';

-- 开始收料（已审核 → 收料中）
UPDATE erp_pick_header
SET status = 3, remark = CONCAT(remark, ' | 仓库开始收料')
WHERE id = 'PH002';

-- 收料完成（收料中 → 已收料）
UPDATE erp_pick_header
SET status = 4, remark = CONCAT(remark, ' | 收料完成')
WHERE id = 'PH002';

-- 入库完成（已收料 → 已入库）
UPDATE erp_pick_header
SET status = 5, remark = CONCAT(remark, ' | 退料已入库')
WHERE id = 'PH002';

-- 关闭单据（已入库 → 已关闭）
UPDATE erp_pick_header
SET status = 6, remark = CONCAT(remark, ' | 单据关闭')
WHERE id = 'PH002';
```

### 9.3 补料单状态流转
```sql
-- 创建补料单
INSERT INTO erp_pick_header (id, default_number, type, work_order_id, task_id,
    depot_id, oper_time, status, total_price, remark, department_id)
VALUES ('PH003', 'BL-20240115-002', 20, 'WO002', 'PT002',
    'D001', NOW(), 0, 450.00, '消耗超标补料', 'DEPT001');

-- 提交审核（待审核 → 审核中）
UPDATE erp_pick_header
SET status = 1, examine_time = NOW()
WHERE id = 'PH003';

-- 审核通过（审核中 → 已审核）
UPDATE erp_pick_header
SET status = 2, examine_id = 'USER003', examine_content = '超标合理，同意补料',
    examine_time = NOW()
WHERE id = 'PH003';

-- 开始发料（已审核 → 发料中）
UPDATE erp_pick_header
SET status = 3, remark = CONCAT(remark, ' | 开始补料发放')
WHERE id = 'PH003';

-- 发料完成（发料中 → 已发料）
UPDATE erp_pick_header
SET status = 4, remark = CONCAT(remark, ' | 补料发放完成')
WHERE id = 'PH003';

-- 确认收料（已发料 → 已确认）
UPDATE erp_pick_header
SET status = 5, remark = CONCAT(remark, ' | 现场确认收到补料')
WHERE id = 'PH003';

-- 关闭单据（已确认 → 已关闭）
UPDATE erp_pick_header
SET status = 6, remark = CONCAT(remark, ' | 单据关闭')
WHERE id = 'PH003';
```

## 10. 业务规则说明

### 10.1 物料单据创建规则
1. **领料单自动生成**：工单下达时根据BOM自动生成领料单
2. **补料单申请条件**：必须有对应的领料单，且消耗超过计划
3. **退料单创建条件**：必须有对应的领料单，且有剩余物料
4. **单据编号规则**：LL/BL/TL + 日期 + 流水号

### 10.2 审核规则
1. **领料单审核**：检查库存充足性、BOM准确性
2. **补料单审核**：分析超标合理性、成本影响评估
3. **退料单审核**：检查退料原因、质量状态确认
4. **审核权限**：生产主管、质检主管、仓库主管

### 10.3 库存影响规则
1. **领料发放**：减少库存，增加在制品
2. **补料发放**：减少库存，增加工单成本
3. **退料入库**：增加库存，减少在制品
4. **批次管理**：严格按批次发放和收回

### 10.4 异常处理规则
1. **库存不足**：自动转采购申请或部分发料
2. **质量异常**：自动记录质量问题，通知相关部门
3. **超时处理**：自动提醒，升级处理
4. **单据冲突**：系统自动检查，防止重复操作

---

**设计说明**：
- 严格按照业务流程设计文档中的物料管理流程设计
- 完全对应 erp_pick_header 和 erp_pick_child 表结构
- 支持三种物料单据类型的完整生命周期管理
- 集成工单和工序任务的物料需求管理
- 提供完整的状态流转和异常处理机制
- 支持批量操作和统计分析功能
