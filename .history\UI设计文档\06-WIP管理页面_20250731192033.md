# WIP管理页面设计（在制品管理）

## 1. 在制品库存查询

### 1.1 在制品库存列表
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📦 在制品库存查询                                                [导出]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 工单号：[_______] 产品：[_______] 当前工序：[全部▼] 状态：[全部▼]         │
│ 存放位置：[全部▼] 批次号：[_______] 日期：[本周▼]      [搜索] [重置]       │
├─────────────────────────────────────────────────────────────────────────────┤
│ 在制品库存列表                                                              │
│ ☐ 工单号      产品名称  规格    当前工序  下道工序  数量  状态    存放位置  │
│ ☑ MO240115001 轴承座    Φ50×100 精加工    装配      95   🟢在制   精加工区  │
│ ☐ MO240115002 法兰盘    Φ80×20  委外      精整      50   🟡委外中 委外厂商  │
│ ☐ MO240115003 连接件    M12×50  质检      入库      200  🔴待转序 质检区    │
│ ☐ MO240115004 支架      L型      装配      包装      80   🟢在制   装配区    │
│ ☐ MO240115005 底座      方形     粗加工    精加工    120  🟢在制   粗加工区  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 库存统计                                                                    │
│ 总在制品：545件  在制：395件  待转序：100件  委外中：50件                  │
│ 工序分布：粗加工120件  精加工95件  装配80件  质检200件  委外50件            │
│ 存放位置：粗加工区120件  精加工区95件  装配区80件  质检区200件  委外50件    │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 在制品详情查看
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 在制品详情 - MO240115001                                         [关闭] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 工单号：MO240115001  产品：轴承座  规格：Φ50×100                          │
│ 物料编码：MAT001  批次号：B240115001  生产日期：2024-01-15                 │
│ 当前数量：95件  单位：件  状态：🟢在制                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序信息                                                                    │
│ 当前工序：精加工  工序编码：OP003  工序序号：3                             │
│ 下道工序：装配  下道工序编码：OP004  工序序号：4                           │
│ 存放位置：精加工区  具体位置：A区-3号工位                                  │
│ 负责人：张师傅  联系方式：13800138001                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转历史                                                                    │
│ 时间              来源工序    目标工序    数量    操作人    备注            │
│ 2024-01-15 09:00  下料       粗加工      100     李师傅    首次投产        │
│ 2024-01-15 14:30  粗加工     精加工      100     王师傅    全部流转        │
│ 2024-01-15 16:00  精加工     质检        5       张师傅    不合格品        │
│ 2024-01-15 17:00  质检       精加工      0       质检员    返工处理        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. 在制品流转记录查询

### 2.1 流转记录列表
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔄 在制品流转记录查询                                            [导出]     │
├─────────────────────────────────────────────────────────────────────────────┤
│ 筛选条件：                                                                  │
│ 流转日期：[今日▼] 来源工序：[全部▼] 目标工序：[全部▼]                    │
│ 工单号：[_______] 流转人：[全部▼] 状态：[全部▼]       [搜索] [重置]        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转记录列表                                                                │
│ 流转单号      工单号      产品    来源工序  目标工序  数量  状态    流转人  │
│ ZX240115001  MO240115001 轴承座  精加工    装配      50   ✅已完成  张师傅  │
│ ZX240115002  MO240115002 法兰盘  粗加工    精加工    30   ✅已完成  王师傅  │
│ ZX240115003  MO240115003 连接件  装配      质检      200  ✅已完成  李师傅  │
│ ZX240115004  MO240115004 支架    质检      入库      80   ✅已完成  质检员  │
│ ZX240115005  MO240115005 底座    下料      粗加工    120  ✅已完成  刘师傅  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转统计                                                                    │
│ 今日流转：8次  平均流转时间：2.3小时  最长等待：4.5小时                   │
│ 工序流转分布：下料→粗加工(2次)  粗加工→精加工(2次)  精加工→装配(2次)      │
│ 委外流转：发料1次  收货1次                                                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 流转记录详情
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 流转记录详情 - ZX240115001                                       [关闭] │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                    │
│ 流转单号：ZX240115001  流转日期：2024-01-15 14:30                         │
│ 工单号：MO240115001  产品：轴承座  规格：Φ50×100                          │
│ 流转人：张师傅  联系方式：13800138001                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转信息                                                                    │
│ 来源工序：精加工  工序编码：OP003  来源位置：精加工区                      │
│ 目标工序：装配    工序编码：OP004  目标位置：装配区                        │
│ 流转数量：50件  单位：件  批次号：B240115001                               │
│ 流转状态：✅已完成  完成时间：2024-01-15 15:15                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 质检信息                                                                    │
│ 质检要求：是  质检类型：工序检验  质检员：李质检                           │
│ 质检时间：2024-01-15 14:45-15:00  质检结果：合格                          │
│ 检验项目：外观检查✅  尺寸检验✅  表面质量✅                               │
│ 质检备注：产品质量良好，符合装配要求                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ 接收信息                                                                    │
│ 接收人：王师傅  接收时间：2024-01-15 15:15                                 │
│ 接收位置：装配区-B区  接收数量：50件                                       │
│ 接收备注：产品状态良好，已开始装配作业                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ 时间轨迹                                                                    │
│ 14:30  张师傅    发出确认    50件产品发出，质量良好                        │
│ 14:45  李质检    开始质检    开始工序检验                                  │
│ 15:00  李质检    质检完成    50件全部合格                                  │
│ 15:15  王师傅    确认接收    50件产品接收完成                              │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 3. 在制品统计分析

### 3.1 在制品统计看板
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📊 在制品统计看板                                       [详细报告] [导出]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 时间范围：[本周▼] 2024年1月15日-21日  统计维度：[工序▼]                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 在制品概览                                                                  │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┐                   │
│ │ 总在制品    │ 在制数量    │ 待转序数量  │ 委外数量    │                   │
│ │    545件    │    395件    │    100件    │    50件     │                   │
│ └─────────────┴─────────────┴─────────────┴─────────────┘                   │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┐                   │
│ │ 在制品价值  │ 平均周转天数│ 流转次数    │ 流转效率    │                   │
│ │  24.8万元   │   3.2天     │    28次     │   92.5%     │                   │
│ └─────────────┴─────────────┴─────────────┴─────────────┘                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 工序在制品分布                                                              │
│ 工序名称    在制数量  占比    平均停留时间  流转频次  瓶颈指数              │
│ 下料        0件      0%      0.5天        高        ✅正常                │
│ 粗加工      120件    22%     1.2天        中        🟡关注                │
│ 精加工      95件     17%     2.1天        低        🔴瓶颈                │
│ 装配        80件     15%     1.8天        中        🟡关注                │
│ 质检        200件    37%     0.8天        高        ✅正常                │
│ 委外        50件     9%      3.5天        低        🔴瓶颈                │
├─────────────────────────────────────────────────────────────────────────────┤
│ 位置分布统计                                                                │
│ 存放位置      数量    占比    利用率    容量状态                            │
│ 粗加工区      120件   22%     85%      🟡接近满载                          │
│ 精加工区      95件    17%     76%      ✅正常                              │
│ 装配区        80件    15%     67%      ✅正常                              │
│ 质检区        200件   37%     95%      🔴接近满载                          │
│ 委外厂商      50件    9%      --       --                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ 趋势分析                                                                    │
│ 本周趋势：在制品总量上升8%，主要集中在质检工序                             │
│ 瓶颈分析：精加工工序停留时间过长，建议增加设备或人员                       │
│ 改善建议：优化质检流程，提高质检效率，减少在制品积压                       │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 在制品流转分析
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔄 在制品流转分析                                       [详细分析] [导出]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 分析维度：[工序流转▼] 时间范围：[本周▼]                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转效率分析                                                                │
│ 流转路径              流转次数  平均时间  最长时间  效率评级                │
│ 下料 → 粗加工         8次      0.5小时   1.2小时   ✅优秀                │
│ 粗加工 → 精加工       6次      2.1小时   4.5小时   🟡一般                │
│ 精加工 → 装配         5次      1.8小时   3.2小时   🟡一般                │
│ 装配 → 质检           7次      0.8小时   1.5小时   ✅良好                │
│ 质检 → 入库           4次      1.2小时   2.8小时   ✅良好                │
│ 精加工 → 委外         2次      3.5小时   6.0小时   🔴较差                │
│ 委外 → 精整           1次      24小时    24小时    🔴较差                │
├─────────────────────────────────────────────────────────────────────────────┤
│ 异常流转分析                                                                │
│ 异常类型        发生次数  占比    主要原因              改善建议            │
│ 超时流转        3次      10.7%   设备故障、人员不足    增加备用设备        │
│ 质量退回        2次      7.1%    工艺问题、操作失误    加强培训            │
│ 位置错误        1次      3.6%    标识不清、沟通不畅    完善标识系统        │
│ 数量差异        1次      3.6%    计数错误、遗漏        双人复核            │
├─────────────────────────────────────────────────────────────────────────────┤
│ 改善建议                                                                    │
│ 1. 精加工工序：增加1台设备，预计可减少50%等待时间                          │
│ 2. 委外管理：建立委外厂商考核机制，要求24小时内完成                        │
│ 3. 质检流程：实施并行质检，减少质检等待时间                                 │
│ 4. 流转标识：统一在制品标识，减少流转错误                                   │
│ 5. 系统优化：实时更新在制品位置，提高数据准确性                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. 数据库字段映射

### 4.1 在制品库存表 (erp_wip_inventory)
```sql
-- 在制品库存查询主要字段
SELECT
    wip.wip_id,                    -- 在制品ID
    wip.production_order_id,       -- 工单号
    wip.material_id,               -- 物料ID
    wip.current_operation_id,      -- 当前工序ID
    wip.next_operation_id,         -- 下道工序ID
    wip.quantity,                  -- 数量
    wip.status,                    -- 状态(在制、待转序、已转序、委外中)
    wip.location,                  -- 存放位置
    wip.batch_no,                  -- 批次号
    wip.create_time,               -- 创建时间
    wip.update_time,               -- 更新时间
    mo.material_name,              -- 产品名称
    mo.specification,              -- 规格
    op1.operation_name as current_op_name,  -- 当前工序名称
    op2.operation_name as next_op_name      -- 下道工序名称
FROM erp_wip_inventory wip
LEFT JOIN erp_machin_header mo ON wip.production_order_id = mo.machin_id
LEFT JOIN erp_operation op1 ON wip.current_operation_id = op1.operation_id
LEFT JOIN erp_operation op2 ON wip.next_operation_id = op2.operation_id
WHERE wip.status IN ('在制', '待转序', '已转序', '委外中')
ORDER BY wip.create_time DESC;
```

### 4.2 在制品流转表 (erp_wip_transfer)
```sql
-- 在制品流转记录查询主要字段
SELECT
    wt.transfer_id,                -- 流转ID
    wt.transfer_code,              -- 流转单号
    wt.production_order_id,        -- 工单号
    wt.material_id,                -- 物料ID
    wt.from_operation_id,          -- 来源工序ID
    wt.to_operation_id,            -- 目标工序ID
    wt.quantity,                   -- 流转数量
    wt.transfer_time,              -- 流转时间
    wt.transfer_by,                -- 流转人
    wt.status,                     -- 状态(已流转)
    wt.quality_check_required,     -- 是否需要质检
    wt.quality_check_result,       -- 质检结果
    wt.notes,                      -- 备注
    mo.material_name,              -- 产品名称
    mo.specification,              -- 规格
    op1.operation_name as from_op_name,    -- 来源工序名称
    op2.operation_name as to_op_name,      -- 目标工序名称
    u.user_name as transfer_user_name      -- 流转人姓名
FROM erp_wip_transfer wt
LEFT JOIN erp_machin_header mo ON wt.production_order_id = mo.machin_id
LEFT JOIN erp_operation op1 ON wt.from_operation_id = op1.operation_id
LEFT JOIN erp_operation op2 ON wt.to_operation_id = op2.operation_id
LEFT JOIN erp_user u ON wt.transfer_by = u.user_id
ORDER BY wt.transfer_time DESC;
```
│ 粗加工区      120件   22%     80%       🟡接近满载                         │
│ 精加工区      95件    17%     63%       ✅正常                             │
│ 装配区        80件    15%     67%       ✅正常                             │
│ 质检区        200件   37%     95%       🔴超负荷                           │
│ 临时存放区    0件     0%      0%        ✅空闲                             │
│ 委外厂商      50件    9%      --        --                                │
├─────────────────────────────────────────────────────────────────────────────┤
│ 流转效率分析                                                                │
│ 流转趋势（最近7天）：                                                      │
│ 8次/天 ┤                                                                   │
│ 6次/天 ┤ ●─●─○─●─●─●─●                                                    │
│ 4次/天 ┤     ○                                                             │
│ 2次/天 ┤                                                                   │
│        └─────────────────────────────────────────────                     │
│         1/15 1/16 1/17 1/18 1/19 1/20 1/21                                │
│ 平均流转时间：2.3小时  目标：2.0小时  ⚠️略高于目标                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 6. 数据库字段映射

### 6.1 在制品库存表 (erp_wip_inventory)
```sql
-- 在制品库存基本信息
id VARCHAR(32) PRIMARY KEY,                 -- 在制品记录ID
material_id VARCHAR(32),                    -- 物料ID
norms_id VARCHAR(32),                       -- 规格ID
production_order_id VARCHAR(32),            -- 生产工单ID (关联erp_machin_header.id)
current_operation_id VARCHAR(32),           -- 当前工序ID
next_operation_id VARCHAR(32),              -- 下一工序ID
quantity DECIMAL(10,2),                     -- 数量
unit VARCHAR(20),                           -- 单位
status VARCHAR(20),                         -- 状态 (在制/待转序/已转序/委外中/不合格)
location VARCHAR(50),                       -- 存放位置
batch_no VARCHAR(50),                       -- 批次号
production_date DATE,                       -- 生产日期
remarks TEXT,                               -- 备注
create_time DATETIME,                       -- 创建时间
update_time DATETIME,                       -- 更新时间
created_by VARCHAR(32),                     -- 创建人
updated_by VARCHAR(32)                      -- 更新人
```

### 6.2 在制品流转表 (erp_wip_transfer)
```sql
-- 在制品流转记录
id VARCHAR(32) PRIMARY KEY,                 -- 流转记录ID
transfer_code VARCHAR(50),                  -- 流转单号 (ZX240115001)
production_order_id VARCHAR(32),            -- 生产工单ID
material_id VARCHAR(32),                    -- 物料ID
from_operation_id VARCHAR(32),              -- 来源工序ID
to_operation_id VARCHAR(32),                -- 目标工序ID
from_task_id VARCHAR(32),                   -- 来源任务ID
to_task_id VARCHAR(32),                     -- 目标任务ID
quantity DECIMAL(10,2),                     -- 流转数量
unit VARCHAR(20),                           -- 单位
batch_no VARCHAR(50),                       -- 批次号
transfer_time DATETIME,                     -- 流转时间
transfer_by VARCHAR(32),                    -- 流转人ID
transfer_by_name VARCHAR(50),               -- 流转人姓名
receiver_id VARCHAR(32),                    -- 接收人ID
receiver_name VARCHAR(50),                  -- 接收人姓名
receive_time DATETIME,                      -- 接收时间
status TINYINT,                             -- 状态 (1-待流转,2-流转中,3-已接收,4-已取消)
from_location VARCHAR(50),                  -- 来源位置
to_location VARCHAR(50),                    -- 目标位置
transfer_reason VARCHAR(200),               -- 流转原因
quality_check_required TINYINT DEFAULT 0,   -- 是否需要质检 (0-否,1-是)
quality_check_result TINYINT,               -- 质检结果 (0-不合格,1-合格,2-待检验)
remarks TEXT,                               -- 备注
create_time DATETIME,                       -- 创建时间
update_time DATETIME                        -- 更新时间
```

### 6.3 在制品调拨表 (erp_wip_allocation)
```sql
-- 在制品调拨管理
id VARCHAR(32) PRIMARY KEY,                 -- 调拨记录ID
allocation_code VARCHAR(50),                -- 调拨单号 (DB240115001)
applicant_id VARCHAR(32),                   -- 申请人ID
applicant_name VARCHAR(50),                 -- 申请人姓名
apply_date DATE,                            -- 申请日期
allocation_reason VARCHAR(200),             -- 调拨原因
urgency_level TINYINT,                      -- 紧急程度 (1-普通,2-紧急,3-特急)
status TINYINT,                             -- 状态 (1-待审批,2-已审批,3-执行中,4-已完成,5-已取消)
approve_time DATETIME,                      -- 审批时间
approver_id VARCHAR(32),                    -- 审批人ID
approver_name VARCHAR(50),                  -- 审批人姓名
plan_start_time DATETIME,                   -- 计划开始时间
plan_end_time DATETIME,                     -- 计划结束时间
actual_start_time DATETIME,                 -- 实际开始时间
actual_end_time DATETIME,                   -- 实际结束时间
executor_id VARCHAR(32),                    -- 执行人ID
executor_name VARCHAR(50),                  -- 执行人姓名
remarks TEXT,                               -- 备注
create_time DATETIME,                       -- 创建时间
update_time DATETIME                        -- 更新时间
```

### 6.4 在制品盘点表 (erp_wip_stocktaking)
```sql
-- 在制品盘点管理
id VARCHAR(32) PRIMARY KEY,                 -- 盘点记录ID
stocktaking_code VARCHAR(50),               -- 盘点单号 (PD240115001)
stocktaking_date DATE,                      -- 盘点日期
stocktaking_type TINYINT,                   -- 盘点类型 (1-定期盘点,2-临时盘点,3-专项盘点)
scope_description TEXT,                     -- 盘点范围描述
responsible_id VARCHAR(32),                 -- 负责人ID
responsible_name VARCHAR(50),               -- 负责人姓名
status TINYINT,                             -- 状态 (1-计划中,2-盘点中,3-已完成,4-已审核)
plan_start_time DATETIME,                   -- 计划开始时间
plan_end_time DATETIME,                     -- 计划结束时间
actual_start_time DATETIME,                 -- 实际开始时间
actual_end_time DATETIME,                   -- 实际结束时间
total_book_qty DECIMAL(10,2),               -- 账面总数量
total_actual_qty DECIMAL(10,2),             -- 实盘总数量
total_difference_qty DECIMAL(10,2),         -- 总差异数量
difference_rate DECIMAL(5,2),               -- 差异率
remarks TEXT,                               -- 备注
create_time DATETIME,                       -- 创建时间
update_time DATETIME                        -- 更新时间
```

## 5. 业务规则说明

### 5.1 在制品状态管理规则
```
在制品状态流转：
在制 → 待转序 → 已转序 → 在制（下道工序）

特殊状态：
- 委外中：委外工序专用状态
- 不合格：质检不合格状态
- 返工中：不合格品返工状态
- 报废：不可修复的废品状态
```

### 5.2 流转控制规则
```
流转前置条件：
1. 上道工序必须完工（部分完工或全部完工）
2. 下道工序必须具备开工条件
3. 质检点工序必须通过质检
4. 委外工序必须完成收货确认

流转数量控制：
- 流转数量 ≤ 当前工序可流转数量
- 下道工序接收数量 = 流转数量 - 质检不合格数量
- 支持部分流转，剩余数量继续在当前工序
```

### 5.3 库存一致性规则
```
数据一致性检查：
1. 各工序在制品数量总和 = 工单投产数量 - 完工入库数量 - 报废数量
2. 流转记录的累计数量 = 各工序接收数量
3. 盘点差异超过阈值（5%）需要审批确认
4. 系统自动校验在制品数量的合理性
```
